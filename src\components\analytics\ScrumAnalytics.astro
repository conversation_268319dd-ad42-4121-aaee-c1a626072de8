---
/**
 * Scrum Analytics Component
 * Following Clean Code: Single responsibility - Scrum-specific velocity analytics
 * Extracted from VelocityPage to enable board-type-specific analytics
 * Enhanced with Cycle Time tab for complete Scrum insights
 */

import VelocityChart from '../velocity/VelocityChart.astro';
import SprintMetrics from '../velocity/SprintMetrics.astro';
import VelocityTrends from '../velocity/VelocityTrends.astro';
import VelocityProgressLoader from '../ui/VelocityProgressLoader.astro';

// Import Kanban components for Cycle Time tab
import KanbanProgressLoader from '../ui/KanbanProgressLoader.astro';
import PeriodSelector from '../ui/PeriodSelector.astro';
import IssueTypeSelector from '../ui/IssueTypeSelector.astro';
import KanbanIssuesModal from './KanbanIssuesModal.astro';

export interface Props {
  boardId: string | null;
  loading?: boolean;
}

const { boardId, loading = false } = Astro.props;
---

<div class="scrum-analytics">
  <!-- Tab Navigation -->
  <div class="tab-navigation mb-6">
    <div class="border-b border-gray-200">
      <nav class="-mb-px flex space-x-8" aria-label="Analytics Tabs">
        <button
          id="velocity-tab"
          class="tab-button active whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
          onclick="switchTab('velocity')"
        >
          📈 Velocity Analysis
        </button>
        <button
          id="cycle-time-tab"
          class="tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
          onclick="switchTab('cycle-time')"
        >
          ⏱️ Cycle Time Analysis
        </button>
      </nav>
    </div>
  </div>

  <!-- Velocity Tab Content -->
  <div id="velocity-tab-content" class="tab-content">
    <div class="velocity-content-section">
    <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
      <!-- Main Velocity Chart -->
      <div class="xl:col-span-2">
        <div id="velocity-chart-container">
          {boardId ? (
            <!-- Initial chart will be loaded via JavaScript -->
            <VelocityChart velocityData={null} loading={true} />
          ) : (
            <VelocityChart velocityData={null} loading={false} />
          )}
        </div>
      </div>
      
      <!-- Current Sprint Metrics -->
      <div class="xl:col-span-1">
        <div id="sprint-metrics-container">
          <SprintMetrics currentSprint={null} loading={boardId ? true : false} />
        </div>
      </div>
    </div>
    
    <!-- Velocity Trends Section -->
    <div class="mt-8">
      <div id="velocity-trends-container">
        <VelocityTrends velocityData={null} loading={boardId ? true : false} />
      </div>
    </div>
  </div>
  </div>

  <!-- Cycle Time Tab Content -->
  <div id="cycle-time-tab-content" class="tab-content hidden">
    <div class="cycle-time-content-section">
      <!-- Filter Selection Section -->
      <div class="filter-selection-section mb-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">
            Analytics Filters
          </h3>
          
          <!-- Filters Grid -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Period Selector -->
            <div>
              <PeriodSelector
                selectedPeriod="last-15-days"
                disabled={!boardId || loading}
                class="period-selector-component"
              />
            </div>
            
            <!-- Issue Type Selector -->
            <div>
              <IssueTypeSelector
                disabled={!boardId || loading}
                class="issue-type-selector-component"
              />
            </div>
          </div>
          
          <!-- Apply Filters Button -->
          <div class="mt-6 flex justify-end">
            <button
              type="button"
              id="apply-filters-button"
              disabled={!boardId || loading}
              class="bg-purple-600 text-white px-6 py-2 rounded-md text-sm font-medium
                     hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2
                     disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              Apply Selection
            </button>
          </div>
        </div>
      </div>

      <!-- Cycle Time Analytics Content -->
      <div class="cycle-time-analytics-section">
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
          <!-- Main Cycle Time Chart -->
          <div class="xl:col-span-2">
            <div id="cycle-time-chart-container">
              <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                  Cycle Time Analysis
                </h3>
                
                {loading && (
                  <div id="initial-loading" class="animate-pulse">
                    <div class="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                    <div class="h-32 bg-gray-200 rounded mb-4"></div>
                    <div class="h-4 bg-gray-200 rounded w-1/2"></div>
                  </div>
                )}
                
                <div id="cycle-time-data" class="hidden">
                  <!-- Cycle Time Percentiles -->
                  <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <div class="text-center p-4 bg-red-50 rounded-lg cursor-help" 
                         title="50% of issues are completed within this time - Fastest performance">
                      <div class="text-2xl font-bold text-red-600" id="p50-value">-</div>
                      <div class="text-sm text-red-800">50th percentile</div>
                      <div class="text-xs text-gray-600">days</div>
                    </div>
                    <div class="text-center p-4 bg-yellow-50 rounded-lg cursor-help" 
                         title="75% of issues are completed within this time">
                      <div class="text-2xl font-bold text-yellow-600" id="p75-value">-</div>
                      <div class="text-sm text-yellow-800">75th percentile</div>
                      <div class="text-xs text-gray-600">days</div>
                    </div>
                    <div class="text-center p-4 bg-green-50 rounded-lg cursor-help" 
                         title="85% of issues are completed within this time">
                      <div class="text-2xl font-bold text-green-600" id="p85-value">-</div>
                      <div class="text-sm text-green-800">85th percentile</div>
                      <div class="text-xs text-gray-600">days</div>
                    </div>
                    <div class="text-center p-4 bg-green-100 rounded-lg cursor-help" 
                         title="95% of issues are completed within this time - Slowest performance">
                      <div class="text-2xl font-bold text-green-800" id="p95-value">-</div>
                      <div class="text-sm text-green-900">95th percentile</div>
                      <div class="text-xs text-gray-600">days</div>
                    </div>
                  </div>
                  
                  <!-- Summary Stats -->
                  <div class="bg-gray-50 p-4 rounded-lg">
                    <div class="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span class="text-gray-600">Total Issues:</span>
                        <span class="font-medium ml-2" id="total-issues">-</span>
                      </div>
                      <div>
                        <span class="text-gray-600">Completed Issues:</span>
                        <span class="font-medium ml-2" id="completed-issues">-</span>
                      </div>
                      <div>
                        <span class="text-gray-600">Sample Size:</span>
                        <span class="font-medium ml-2" id="sample-size">-</span>
                      </div>
                      <div>
                        <span class="text-gray-600">Last Updated:</span>
                        <span class="font-medium ml-2" id="last-updated">-</span>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Cycle Time Probability Table -->
                  <div id="cycle-time-probability" class="mt-6">
                    <h4 class="text-md font-medium text-gray-900 mb-3">Cycle Time Probability Distribution</h4>
                    <div class="overflow-x-auto">
                      <table class="w-full border-collapse bg-white probability-table">
                        <thead>
                          <tr class="bg-gray-50">
                            <th class="border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-900 w-20">
                              Days
                            </th>
                            <th class="border border-gray-200 px-3 py-3 text-left text-sm font-medium text-gray-900 w-20">
                              Issues Count
                            </th>
                            <th class="border border-gray-200 px-3 py-3 text-left text-sm font-medium text-gray-900 w-30">
                              Probability that an issue is going to take this long
                            </th>
                            <th class="border border-gray-200 px-3 py-3 text-left text-sm font-medium text-gray-900 w-30">
                              Confidence that an issue will take this long or less
                            </th>
                          </tr>
                        </thead>
                        <tbody id="probability-table-body">
                          <!-- Table rows will be populated dynamically -->
                        </tbody>
                      </table>
                    </div>
                    
                    <!-- Recommendations -->
                    <div id="probability-recommendations" class="mt-4 p-4 bg-blue-50 rounded-lg">
                      <div class="flex items-start">
                        <div class="text-blue-500 mr-3 mt-1">💡</div>
                        <div>
                          <h5 class="font-medium text-blue-900 mb-1">Recommendation</h5>
                          <p class="text-sm text-blue-800" id="recommendation-text">
                            Loading recommendations...
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div id="cycle-time-placeholder" class={!loading ? "text-center py-12" : "hidden"}>
                  <div class="text-6xl mb-4">⏱️</div>
                  <h4 class="text-xl font-medium text-gray-900 mb-2">
                    Select a Board
                  </h4>
                  <p class="text-gray-600 mb-6">
                    Choose a board to view cycle time percentiles and flow metrics.
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Current Flow Metrics -->
          <div class="xl:col-span-1">
            <div id="flow-metrics-container">
              {loading ? (
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    Flow Metrics
                  </h3>
                  <div class="animate-pulse">
                    <div class="h-4 bg-gray-200 rounded w-1/3 mb-3"></div>
                    <div class="h-8 bg-gray-200 rounded mb-4"></div>
                    <div class="grid grid-cols-2 gap-4">
                      <div class="h-16 bg-gray-200 rounded"></div>
                      <div class="h-16 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                </div>
              ) : (
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    Flow Metrics
                  </h3>
                  <div class="text-center py-8">
                    <div class="text-4xl mb-3">📊</div>
                    <p class="text-gray-600 text-sm">
                      Flow metrics will show throughput, WIP limits, and cycle time trends.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        
        <!-- Cycle Time Insights Section -->
        <div class="mt-8">
          <div id="cycle-time-insights-container">
            {loading ? (
              <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                  Flow Insights
                </h3>
                <div class="animate-pulse">
                  <div class="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="h-24 bg-gray-200 rounded"></div>
                    <div class="h-24 bg-gray-200 rounded"></div>
                  </div>
                </div>
              </div>
            ) : (
              <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                  Flow Insights
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div class="insight-card p-4 bg-purple-50 rounded-lg">
                    <div class="flex items-start">
                      <div class="text-purple-500 mr-3 mt-1">🔄</div>
                      <div>
                        <h5 class="font-medium text-purple-900 mb-1">Delivery Time Analysis</h5>
                        <p class="text-sm text-purple-800">
                          Understand how long it takes your Scrum team to deliver work items from start to finish.
                        </p>
                      </div>
                    </div>
                  </div>
                  <div class="insight-card p-4 bg-green-50 rounded-lg">
                    <div class="flex items-start">
                      <div class="text-green-500 mr-3 mt-1">⚡</div>
                      <div>
                        <h5 class="font-medium text-green-900 mb-1">Predictability Enhancement</h5>
                        <p class="text-sm text-green-800">
                          Use cycle time data alongside velocity to improve sprint planning and delivery predictability.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      
      <!-- Issues Details Section for Cycle Time -->
      <div class="issues-details-section mt-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">
            Issues Used for Percentile Calculation
          </h3>
          
          <div id="issues-details-content" class="hidden">
            <!-- Summary Info -->
            <div class="mb-4 p-4 bg-blue-50 rounded-lg">
              <div class="flex items-center text-sm text-blue-800">
                <div class="text-blue-500 mr-2">📊</div>
                <span>Showing <span id="issues-count" class="font-medium">0</span> completed issues used for cycle time percentiles calculation</span>
              </div>
            </div>
            
            <!-- Issues Table -->
            <div class="overflow-x-auto">
              <table class="w-full border-collapse bg-white">
                <thead>
                  <tr class="bg-gray-50">
                    <th class="border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-900">
                      Type
                    </th>
                    <th class="border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-900">
                      Issue Key
                    </th>
                    <th class="border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-900">
                      Summary
                    </th>
                    <th class="border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-900">
                      Status
                    </th>
                    <th class="border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-900">
                      Cycle Time
                    </th>
                  </tr>
                </thead>
                <tbody id="issues-details-table-body">
                  <!-- Table rows will be populated dynamically -->
                </tbody>
              </table>
            </div>
            
            <!-- Empty State -->
            <div id="issues-details-empty" class="hidden text-center py-8">
              <div class="text-4xl mb-2">📋</div>
              <p class="text-gray-600">No completed issues found for the selected filters.</p>
            </div>
          </div>
          
          <!-- Placeholder when no board selected -->
          <div id="issues-details-placeholder" class="text-center py-8">
            <div class="text-4xl mb-2">📝</div>
            <p class="text-gray-600">Select a board and apply filters to see the issues used for cycle time calculations.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Loading overlay for dynamic updates -->
  <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
    <div id="progress-loader-container">
      <VelocityProgressLoader 
        boardId={boardId || 'unknown'}
        stage="quick"
        progress={0}
        message="Initializing velocity analysis..."
        allowCancel={true}
      />
    </div>
  </div>
  
  <!-- Cycle Time Loading overlay -->
  <div id="kanban-loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
    <div id="kanban-progress-loader-container">
      <KanbanProgressLoader 
        boardId={boardId || 'unknown'}
        stage="issues"
        progress={0}
        message="Initializing cycle time analysis..."
        allowCancel={true}
      />
    </div>
  </div>
  
  <!-- Kanban Issues Modal for Cycle Time tab -->
  <KanbanIssuesModal isOpen={false} />
</div>

<style>
  .scrum-analytics {
    @apply w-full;
  }
  
  .velocity-content-section {
    @apply w-full;
  }
  
  .cycle-time-content-section {
    @apply w-full;
  }
  
  .tab-navigation {
    @apply w-full;
  }
  
  .tab-button {
    @apply border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300;
    transition: all 0.2s ease;
  }
  
  .tab-button.active {
    @apply border-purple-500 text-purple-600;
  }
  
  .tab-content {
    @apply w-full;
  }
  
  .insight-card {
    @apply transition-transform duration-200;
  }
  
  .insight-card:hover {
    @apply transform scale-105;
  }
  
  .probability-table {
    @apply border-collapse w-full;
  }
  
  .probability-row {
    @apply transition-colors duration-200;
  }
  
  .probability-row:hover {
    @apply bg-gray-50;
  }
  
  .recommended-range {
    @apply border-2 border-dashed border-orange-400;
    background-color: rgba(251, 191, 36, 0.1);
  }
  
  .recommended-range td {
    @apply font-medium;
  }
  
  .confidence-cell {
    @apply transition-all duration-300;
  }
</style>

<script>
  // Scrum Analytics Client-side Logic
  // Following Clean Code: Express intent, single responsibility per function
  
  interface VelocityData {
    boardId: string;
    boardName: string;
    closedSprints: any[];
    activeSprint: any | null;
    averageVelocity: number;
    trend: string;
    predictability: number;
    totalSprintsAnalyzed: number;
    averageSprintCompletionRate: number;
  }
  
  let currentBoardId: string | null = null;
  let isLoading = false;
  let cancelController: AbortController | null = null;
  
  // Lazy loading state tracking
  let velocityDataLoaded = false;
  let cycleTimeDataLoaded = false;
  
  /**
   * Shows loading overlay with progress tracking
   * Following Clean Code: Clear function name, single responsibility
   */
  function showLoading() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
      overlay.classList.remove('hidden');
      isLoading = true;
    }
  }
  
  /**
   * Hides loading overlay and resets state
   * Following Clean Code: Clear function name, defensive programming
   */
  function hideLoading() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
      overlay.classList.add('hidden');
      isLoading = false;
    }
  }
  
  /**
   * Multi-stage data fetching with progress tracking
   * Following Clean Code: Express intent, error handling
   */
  async function fetchVelocityDataMultiStage(boardId: string): Promise<VelocityData | null> {
    console.log('Starting multi-stage velocity loading for board:', boardId);

    // Map trend values from multi-stage format to legacy format
    const mapTrend = (trend: string): string => {
      switch (trend) {
        case 'up': return 'increasing';
        case 'down': return 'decreasing';
        case 'stable': return 'stable';
        case 'no-data': return 'stable';
        default: return 'stable';
      }
    };

    try {
      // Import multi-stage loader dynamically
      const { loadVelocityDataMultiStage } = await import('../../lib/velocity/multi-stage-loader');

      // Configure for Vercel limitations
      const config = {
        maxIssuesPerBatch: 150,
        maxSprintsPerBatch: 6,
        enableParallelBatches: false,
        stageTimeoutMs: 45000
      };

      // Progress callback for UI updates
      const onProgress = (progress: any) => {
        console.log(`[${progress.stage}] ${progress.percentage}%: ${progress.message}`);
        updateProgressLoader({
          stage: progress.stage,
          currentSprint: progress.completed,
          totalSprints: progress.total,
          message: progress.message,
          percentage: progress.percentage
        });
      };

      // Load data with progress tracking
      const combinedData = await loadVelocityDataMultiStage(boardId, config, onProgress);

      // Transform to legacy format for compatibility
      // Following Clean Code: Defensive programming, handle edge cases
      const velocityData: VelocityData = {
        boardId: combinedData.boardId,
        boardName: combinedData.boardName,
        closedSprints: combinedData.closedSprints ? [...combinedData.closedSprints] : [],
        activeSprint: combinedData.activeSprint,
        averageVelocity: combinedData.averageVelocity,
        trend: mapTrend(combinedData.trend),
        predictability: combinedData.predictability,
        totalSprintsAnalyzed: combinedData.summary?.totalSprintsAnalyzed || 0,
        averageSprintCompletionRate: combinedData.averageSprintCompletionRate || 0
      };

      console.log(`Multi-stage loading complete: ${velocityData.totalSprintsAnalyzed} sprints analyzed`);
      return velocityData;

    } catch (error) {
      console.error('Multi-stage loading failed:', error);

      // Fallback to quick endpoint only
      try {
        console.log('Falling back to quick-only loading...');
        const response = await fetch(`/api/velocity/${boardId}/quick`);

        if (response.ok) {
          const quickData = await response.json();

          return {
            boardId: quickData.boardId,
            boardName: quickData.boardName,
            closedSprints: (quickData.sprints || []).filter((s: any) => s.sprint.state === 'closed'),
            activeSprint: (quickData.sprints || []).find((s: any) => s.sprint.state === 'active') || null,
            averageVelocity: Math.round(quickData.averageVelocity || 0),
            trend: mapTrend(quickData.trend || 'stable'),
            predictability: quickData.predictability || 0,
            totalSprintsAnalyzed: quickData.sprintsAnalyzed || 0,
            averageSprintCompletionRate: 0  // Not available in quick endpoint, set default
          };
        }
      } catch (fallbackError) {
        console.error('Fallback to quick endpoint also failed:', fallbackError);
      }

      throw error;
    }
  }

  /**
   * Updates the progress loader with current progress data
   * Following Clean Code: Single responsibility, DOM manipulation
   */
  function updateProgressLoader(progressData: any) {
    const container = document.getElementById('progress-loader-container');
    if (!container) return;

    // Create updated progress loader HTML
    const progressHTML = createProgressLoaderHTML(progressData);
    container.innerHTML = progressHTML;
  }

  /**
   * Creates HTML for the progress loader component
   * Following Clean Code: Template generation, express intent
   */
  function createProgressLoaderHTML(progressData: any): string {
    const { stage, currentSprint, totalSprints, message, percentage } = progressData;
    const progressPercentage = totalSprints > 0 ? Math.round((currentSprint / totalSprints) * 100) : percentage || 0;

    return `
      <div class="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto">
        <div class="text-center">
          <div class="text-lg font-semibold text-gray-900 mb-2">Loading Velocity Data</div>
          <div class="text-sm text-gray-600 mb-4">${message}</div>

          <div class="w-full bg-gray-200 rounded-full h-3 mb-4">
            <div class="bg-blue-500 h-3 rounded-full transition-all duration-300" style="width: ${progressPercentage}%"></div>
          </div>

          <div class="text-xs text-gray-500">
            Stage: ${stage} • ${progressPercentage}% complete
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Updates the velocity chart with new data
   * Following Clean Code: Clear function name, handles both success and error states
   */
  function updateVelocityChart(velocityData: VelocityData | null) {
    const container = document.getElementById('velocity-chart-container');
    if (!container) return;
    
    if (velocityData) {
      // Store velocity data globally for modal access
      (window as any).currentVelocityData = velocityData;
      
      // Create chart HTML dynamically
      container.innerHTML = createVelocityChartHTML(velocityData);
      
      // Re-attach event listeners for sprint rows after dynamic content creation
      attachSprintRowListeners();
    } else {
      container.innerHTML = '<div class="text-center text-gray-500 py-8">Failed to load velocity data</div>';
    }
  }
  
  /**
   * Updates sprint metrics with current sprint data
   * Following Clean Code: Express intent, single responsibility
   */
  function updateSprintMetrics(velocityData: VelocityData | null) {
    const container = document.getElementById('sprint-metrics-container');
    if (!container) return;
    
    if (velocityData?.activeSprint) {
      container.innerHTML = createSprintMetricsHTML(velocityData.activeSprint);
    } else {
      container.innerHTML = createEmptySprintMetricsHTML();
    }
  }
  
  /**
   * Updates velocity trends with historical data
   * Following Clean Code: Express intent, single responsibility
   */
  function updateVelocityTrends(velocityData: VelocityData | null) {
    const container = document.getElementById('velocity-trends-container');
    if (!container) return;
    
    if (velocityData) {
      container.innerHTML = createVelocityTrendsHTML(velocityData);
    } else {
      container.innerHTML = '<div class="text-center text-gray-500 py-8">Failed to load trends data</div>';
    }
  }
  
  /**
   * Loads Scrum analytics data for a specific board
   * Following Clean Code: Express intent, error handling
   */
  async function loadScrumAnalytics(boardId: string) {
    if (!boardId) return;
    
    // Only load if not already loaded for this board
    if (boardId === currentBoardId && velocityDataLoaded) {
      console.log('[ScrumAnalytics] Velocity data already loaded for board:', boardId);
      return;
    }
    
    currentBoardId = boardId;
    showLoading();
    
    try {
      const velocityData = await fetchVelocityDataMultiStage(boardId);
      updateVelocityChart(velocityData);
      updateSprintMetrics(velocityData);
      updateVelocityTrends(velocityData);
      velocityDataLoaded = true;
      console.log('[ScrumAnalytics] Velocity data loaded and cached for board:', boardId);
    } catch (error) {
      console.error('Failed to load Scrum analytics:', error);
      updateVelocityChart(null);
      updateSprintMetrics(null);
      updateVelocityTrends(null);
      velocityDataLoaded = false;
    } finally {
      hideLoading();
    }
  }
  
  /**
   * Creates HTML for velocity chart
   * Following Clean Code: Template generation, express intent
   */
  function createVelocityChartHTML(data: VelocityData): string {
    const getTrendIcon = (trend: string) => {
      switch (trend) {
        case 'increasing': return '📈';
        case 'decreasing': return '📉';
        case 'stable': return '📊';
        default: return '📊';
      }
    };

    const getTrendText = (trend: string) => {
      switch (trend) {
        case 'increasing': return 'Increasing';
        case 'decreasing': return 'Decreasing';
        case 'stable': return 'Stable';
        default: return 'Unknown';
      }
    };

    const getTrendColor = (trend: string) => {
      switch (trend) {
        case 'increasing': return 'text-green-600';
        case 'decreasing': return 'text-red-600';
        case 'stable': return 'text-blue-600';
        default: return 'text-gray-600';
      }
    };

    const getCompletionRateColor = (value: number) => {
      if (value >= 85) return 'text-green-600';
      if (value >= 70) return 'text-yellow-600';
      return 'text-red-600';
    };

    // Use only closed sprints for velocity analysis (last 5 closed sprints)
    // Following Clean Code: Defensive programming, handle edge cases
    const recentClosedSprints = data.closedSprints ? data.closedSprints.slice(-5) : [];

    return `
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Velocity Analysis</h3>

        <div class="board-header mb-6">
          <h4 class="font-medium text-gray-900">${data.boardName}</h4>
          <p class="text-sm text-gray-600">${data.totalSprintsAnalyzed} closed sprints analyzed</p>
          ${data.activeSprint ?
            `<p class="text-sm text-blue-600 mt-1">Active: ${data.activeSprint.sprint.name}</p>` :
            ''
          }
        </div>

        <div class="metrics-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div class="metric-card bg-blue-50 p-4 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">${data.averageVelocity}</div>
            <div class="text-sm text-blue-800">Average Velocity</div>
          </div>
          <div class="metric-card bg-green-50 p-4 rounded-lg">
            <div class="flex items-center space-x-2 ${getTrendColor(data.trend)}">
              <span class="text-3xl">${getTrendIcon(data.trend)}</span>
              <span class="text-xl font-bold">${getTrendText(data.trend)}</span>
            </div>
            <div class="text-sm text-green-600">Trend</div>
          </div>
          <div class="metric-card bg-purple-50 p-4 rounded-lg">
            <div class="text-2xl font-bold text-purple-600">${data.predictability}%</div>
            <div class="text-sm text-purple-800">Predictability</div>
          </div>
          <div class="metric-card bg-orange-50 p-4 rounded-lg">
            <div class="text-2xl font-bold ${getCompletionRateColor(data.averageSprintCompletionRate)}">${data.averageSprintCompletionRate}%</div>
            <div class="text-sm text-orange-800">Sprint Completion</div>
          </div>
        </div>

        <div class="sprints-list">
          <h5 class="font-medium text-gray-900 mb-3">Last 5 Closed Sprints</h5>
          <div class="space-y-2">
            ${recentClosedSprints.length > 0 ? recentClosedSprints.reverse().map(sv => `
              <div
                class="sprint-row p-3 bg-gray-50 rounded cursor-pointer hover:bg-gray-100 transition-colors duration-150"
                data-sprint-id="${sv.sprint.id}"
                data-sprint-name="${sv.sprint.name}"
                onclick="openSprintModal('${sv.sprint.id}', '${sv.sprint.name}')"
                role="button"
                tabindex="0"
                aria-label="View issues for ${sv.sprint.name}"
              >
                <!-- Sprint Header -->
                <div class="flex justify-between items-center mb-2">
                  <div>
                    <div class="font-medium text-sm">${sv.sprint.name}</div>
                    <div class="text-xs text-gray-600">Closed • Click to view issues</div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <div class="text-right">
                      <div class="font-medium text-sm">${sv.completedPoints}/${sv.committedPoints}</div>
                      <div class="text-xs text-gray-600">${sv.completionRate}% complete</div>
                    </div>
                    <div class="text-gray-400">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                      </svg>
                    </div>
                  </div>
                </div>

                <!-- Progress Bar - Following Clean Code: Visual consistency with Current Sprint Metrics -->
                <div class="mt-2">
                  <div class="flex items-center justify-between mb-1">
                    <span class="text-xs font-medium text-gray-600">Sprint Progress</span>
                    <span class="text-xs text-gray-500">${sv.completedPoints} / ${sv.committedPoints} points</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div
                      class="h-2 rounded-full transition-all duration-300 ${
                        sv.completionRate >= 80
                          ? 'bg-green-500'
                          : sv.completionRate >= 60
                            ? 'bg-yellow-500'
                            : 'bg-red-500'
                      }"
                      style="width: ${Math.min(sv.completionRate, 100)}%"
                    ></div>
                  </div>
                </div>
              </div>
            `).join('') : '<div class="text-center text-gray-500 py-4">No closed sprints available</div>'}
          </div>
          ${recentClosedSprints.length < 5 && recentClosedSprints.length > 0 ?
            `<p class="text-xs text-gray-500 mt-2 text-center">Showing ${recentClosedSprints.length} of 5 recent closed sprints</p>` :
            ''
          }
        </div>
      </div>
    `;
  }

  /**
   * Creates HTML for velocity trends
   * Following Clean Code: Separate concerns, focused function
   */
  function createVelocityTrendsHTML(velocityData: VelocityData): string {
    const getTrendIcon = (trend: string) => {
      switch (trend) {
        case 'increasing': return '📈';
        case 'decreasing': return '📉';
        case 'stable': return '📊';
        default: return '📊';
      }
    };

    const getTrendColor = (trend: string) => {
      switch (trend) {
        case 'increasing': return 'text-green-600';
        case 'decreasing': return 'text-red-600';
        case 'stable': return 'text-blue-600';
        default: return 'text-gray-600';
      }
    };

    const getPredictabilityColor = (predictability: number) => {
      if (predictability >= 80) return 'text-green-600';
      if (predictability >= 60) return 'text-yellow-600';
      return 'text-red-600';
    };

    return `
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Velocity Trends & Analysis</h3>

        <div class="trend-summary grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div class="trend-card bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg">
            <h4 class="text-lg font-medium text-gray-900 mb-2">Velocity Trend</h4>
            <div class="flex items-center space-x-2 ${getTrendColor(velocityData.trend)}">
              <span class="text-4xl">${getTrendIcon(velocityData.trend)}</span>
              <span class="text-2xl font-bold">${velocityData.trend.charAt(0).toUpperCase() + velocityData.trend.slice(1)}</span>
            </div>
            <p class="text-sm text-gray-600 mt-2">Based on ${velocityData.totalSprintsAnalyzed} closed sprints</p>
          </div>

          <div class="predictability-card bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-lg">
            <h4 class="text-lg font-medium text-gray-900 mb-2">Predictability</h4>
            <div class="${getPredictabilityColor(velocityData.predictability)} text-2xl font-bold">${velocityData.predictability}%</div>
            <p class="text-sm text-gray-600 mt-2">${
              velocityData.predictability >= 80 ? 'High' :
              velocityData.predictability >= 60 ? 'Medium' : 'Low'
            } consistency</p>
          </div>
        </div>

        <div class="insights-section">
          <h4 class="text-lg font-medium text-gray-900 mb-4">Key Insights</h4>
          <div class="insights-grid grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="insight-card p-4 bg-blue-50 rounded-lg">
              <div class="flex items-start">
                <div class="text-blue-500 mr-3 mt-1">💡</div>
                <div>
                  <h5 class="font-medium text-blue-900 mb-1">Average Velocity</h5>
                  <p class="text-sm text-blue-800">Team completes an average of ${velocityData.averageVelocity} story points per sprint.</p>
                </div>
              </div>
            </div>
            <div class="insight-card p-4 bg-green-50 rounded-lg">
              <div class="flex items-start">
                <div class="text-green-500 mr-3 mt-1">📊</div>
                <div>
                  <h5 class="font-medium text-green-900 mb-1">Team Performance</h5>
                  <p class="text-sm text-green-800">${
                    velocityData.predictability >= 80 ? 'Highly consistent delivery with reliable planning.' :
                    velocityData.predictability >= 60 ? 'Good consistency with room for improvement.' :
                    'Variable performance suggests planning challenges.'
                  }</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Attaches event listeners to sprint rows
   * Following Clean Code: Single responsibility, clear intent
   */
  function attachSprintRowListeners() {
    const sprintRows = document.querySelectorAll('.sprint-row[data-sprint-id]');
    sprintRows.forEach(row => {
      const sprintId = row.getAttribute('data-sprint-id');
      const sprintName = row.getAttribute('data-sprint-name');

      if (sprintId && sprintName) {
        // Add keyboard event listener
        row.addEventListener('keydown', (event) => {
          const keyEvent = event as KeyboardEvent;
          if (keyEvent.key === 'Enter' || keyEvent.key === ' ') {
            keyEvent.preventDefault();
            (window as any).openSprintModal?.(sprintId, sprintName);
          }
        });
      }
    });
  }

  /**
   * Creates HTML for empty sprint metrics
   * Following Clean Code: Template generation, defensive programming
   */
  function createEmptySprintMetricsHTML(): string {
    return `
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Current Sprint Metrics</h3>
        <div class="text-center text-gray-500 py-8">No active sprint found</div>
      </div>
    `;
  }

  /**
   * Creates HTML for sprint metrics
   * Following Clean Code: Template-like function, clear structure
   */
  function createSprintMetricsHTML(currentSprint: any): string {
    if (!currentSprint) {
      return createEmptySprintMetricsHTML();
    }

    const getSprintStatusColor = (state: string) => {
      switch (state) {
        case 'active': return 'bg-blue-100 text-blue-800';
        case 'closed': return 'bg-green-100 text-green-800';
        case 'future': return 'bg-gray-100 text-gray-800';
        default: return 'bg-gray-100 text-gray-800';
      }
    };

    const getCompletionStatusColor = (rate: number) => {
      if (rate >= 80) return 'text-green-600';
      if (rate >= 60) return 'text-yellow-600';
      return 'text-red-600';
    };

    return `
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Current Sprint Metrics</h3>

        <div class="sprint-header mb-6">
          <div class="flex items-center justify-between mb-2">
            <h4 class="text-xl font-medium text-gray-900">${currentSprint.sprint.name}</h4>
            <span class="px-2 py-1 text-xs font-medium rounded-full ${getSprintStatusColor(currentSprint.sprint.state)}">
              ${currentSprint.sprint.state}
            </span>
          </div>
          ${currentSprint.sprint.goal ? `<p class="text-sm text-gray-600 mb-4"><strong>Goal:</strong> ${currentSprint.sprint.goal}</p>` : ''}
        </div>

        <div class="progress-metrics grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div class="metric-card bg-blue-50 p-4 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">${currentSprint.committedPoints}</div>
            <div class="text-sm text-blue-800">Committed Points</div>
          </div>
          <div class="metric-card bg-green-50 p-4 rounded-lg">
            <div class="text-2xl font-bold text-green-600">${currentSprint.completedPoints}</div>
            <div class="text-sm text-green-800">Completed Points</div>
          </div>
          <div class="metric-card bg-purple-50 p-4 rounded-lg">
            <div class="text-2xl font-bold ${getCompletionStatusColor(currentSprint.completionRate)}">${currentSprint.completionRate}%</div>
            <div class="text-sm text-purple-800">Completion Rate</div>
          </div>
        </div>

        <div class="progress-section">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-gray-700">Sprint Progress</span>
            <span class="text-sm text-gray-600">${currentSprint.completedPoints} / ${currentSprint.committedPoints} points</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-3">
            <div class="h-3 rounded-full transition-all duration-300 ${
              currentSprint.completionRate >= 80 ? 'bg-green-500' :
              currentSprint.completionRate >= 60 ? 'bg-yellow-500' : 'bg-red-500'
            }" style="width: ${Math.min(currentSprint.completionRate, 100)}%"></div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Switches between Velocity and Cycle Time tabs with lazy loading
   * Following Clean Code: Express intent, single responsibility
   */
  function switchTab(tabName: string) {
    const velocityTab = document.getElementById('velocity-tab');
    const cycleTimeTab = document.getElementById('cycle-time-tab');
    const velocityContent = document.getElementById('velocity-tab-content');
    const cycleTimeContent = document.getElementById('cycle-time-tab-content');
    
    if (!velocityTab || !cycleTimeTab || !velocityContent || !cycleTimeContent) {
      console.error('[ScrumAnalytics] Tab elements not found');
      return;
    }
    
    // Update tab buttons
    velocityTab.classList.remove('active');
    cycleTimeTab.classList.remove('active');
    
    // Hide all content
    velocityContent.classList.add('hidden');
    cycleTimeContent.classList.add('hidden');
    
    if (tabName === 'velocity') {
      velocityTab.classList.add('active');
      velocityContent.classList.remove('hidden');
      console.log('[ScrumAnalytics] Switched to Velocity tab');
      
      // Lazy load velocity data if board is selected and not already loaded
      if (currentBoardId && !velocityDataLoaded) {
        loadScrumAnalytics(currentBoardId);
      }
      
      // Save preference
      localStorage.setItem('scrumAnalyticsActiveTab', 'velocity');
    } else if (tabName === 'cycle-time') {
      cycleTimeTab.classList.add('active');
      cycleTimeContent.classList.remove('hidden');
      console.log('[ScrumAnalytics] Switched to Cycle Time tab');
      
      // Lazy load cycle time data if board is selected and not already loaded
      if (currentBoardId && !cycleTimeDataLoaded) {
        loadCycleTimeForCurrentBoard();
      }
      
      // Save preference
      localStorage.setItem('scrumAnalyticsActiveTab', 'cycle-time');
    }
  }
  
  /**
   * Loads cycle time data for the current board
   * Reuses kanban analytics functionality
   * Following Clean Code: Express intent, code reuse
   */
  async function loadCycleTimeForCurrentBoard() {
    if (!currentBoardId) return;
    
    // Only load if not already loaded for this board
    if (cycleTimeDataLoaded) {
      console.log('[ScrumAnalytics] Cycle time data already loaded for board:', currentBoardId);
      return;
    }
    
    console.log('[ScrumAnalytics] Loading cycle time data for board:', currentBoardId);
    
    try {
      // Import cycle time functionality from KanbanAnalytics
      const kanbanAnalytics = (window as any).kanbanAnalytics;
      if (kanbanAnalytics && kanbanAnalytics.loadKanbanAnalytics) {
        await kanbanAnalytics.loadKanbanAnalytics(currentBoardId);
        cycleTimeDataLoaded = true;
        console.log('[ScrumAnalytics] Cycle time data loaded and cached for board:', currentBoardId);
      } else {
        // Fallback: load cycle time data directly
        await loadCycleTimeDataDirect(currentBoardId);
      }
    } catch (error) {
      console.error('[ScrumAnalytics] Failed to load cycle time data:', error);
      showCycleTimeError('Failed to load cycle time data');
      cycleTimeDataLoaded = false;
    }
  }
  
  /**
   * Direct cycle time data loading (fallback when kanbanAnalytics not available)
   * Following Clean Code: Fallback strategy, error handling
   */
  async function loadCycleTimeDataDirect(boardId: string) {
    console.log('[ScrumAnalytics] Loading cycle time data directly for board:', boardId);
    
    showCycleTimeLoading();
    
    try {
      const response = await fetch(`/api/kanban/${boardId}/analytics?period=last-15-days`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      displayCycleTimeData(data);
      cycleTimeDataLoaded = true;
      console.log('[ScrumAnalytics] Cycle time data loaded directly for board:', boardId);
      
    } catch (error) {
      console.error('[ScrumAnalytics] Direct cycle time loading failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      showCycleTimeError(errorMessage);
      cycleTimeDataLoaded = false;
    } finally {
      hideCycleTimeLoading();
    }
  }
  
  /**
   * Shows loading state for cycle time tab
   * Following Clean Code: Single responsibility, consistent UI
   */
  function showCycleTimeLoading() {
    const overlay = document.getElementById('kanban-loading-overlay');
    if (overlay) {
      overlay.classList.remove('hidden');
    }
  }
  
  /**
   * Hides loading state for cycle time tab
   * Following Clean Code: Single responsibility, consistent UI
   */
  function hideCycleTimeLoading() {
    const overlay = document.getElementById('kanban-loading-overlay');
    if (overlay) {
      overlay.classList.add('hidden');
    }
  }
  
  /**
   * Displays cycle time data (reuses KanbanAnalytics logic)
   * Following Clean Code: Code reuse, consistent behavior
   */
  function displayCycleTimeData(data: any) {
    console.log('[ScrumAnalytics] Displaying cycle time data');
    
    // Find the cycle time chart container
    const chartContainer = document.querySelector('#cycle-time-chart-container');
    if (!chartContainer) {
      console.error('[ScrumAnalytics] Cycle time chart container not found');
      return;
    }
    
    // Hide loading, placeholder and show data
    const initialLoading = chartContainer.querySelector('#initial-loading');
    const placeholder = chartContainer.querySelector('#cycle-time-placeholder');
    const dataContainer = chartContainer.querySelector('#cycle-time-data');
    
    if (initialLoading) initialLoading.classList.add('hidden');
    if (placeholder) placeholder.classList.add('hidden');
    if (dataContainer) dataContainer.classList.remove('hidden');
    
    // Update percentile values
    const updateElement = (id: string, value: number) => {
      const element = chartContainer.querySelector(`#${id}`);
      if (element) {
        // Convert hours to days (24 hours per day)
        const days = value / 24;
        element.textContent = days.toFixed(1);
      }
    };
    
    updateElement('p50-value', data.cycleTimePercentiles.p50);
    updateElement('p75-value', data.cycleTimePercentiles.p75);
    updateElement('p85-value', data.cycleTimePercentiles.p85);
    updateElement('p95-value', data.cycleTimePercentiles.p95);
    
    // Update summary stats
    const updateTextElement = (id: string, value: string | number) => {
      const element = chartContainer.querySelector(`#${id}`);
      if (element) {
        element.textContent = value.toString();
      }
    };
    
    updateTextElement('total-issues', data.totalIssues);
    updateTextElement('completed-issues', data.completedIssues);
    updateTextElement('sample-size', data.cycleTimePercentiles.sampleSize);
    updateTextElement('last-updated', new Date(data.calculatedAt).toLocaleString());
    
    // Display probability table if available
    if (data.cycleTimeProbability) {
      displayProbabilityTable(data.cycleTimeProbability, chartContainer);
    }
    
    // Display issues details table
    displayIssuesDetails(data.issuesDetails);
  }
  
  /**
   * Shows error state for cycle time tab
   * Following Clean Code: Consistent error handling
   */
  function showCycleTimeError(errorMessage: string) {
    console.error('[ScrumAnalytics] Cycle time error:', errorMessage);
    
    const chartContainer = document.querySelector('#cycle-time-chart-container');
    if (!chartContainer) return;
    
    const initialLoading = chartContainer.querySelector('#initial-loading');
    const dataContainer = chartContainer.querySelector('#cycle-time-data');
    const placeholder = chartContainer.querySelector('#cycle-time-placeholder');
    
    if (initialLoading) initialLoading.classList.add('hidden');
    if (dataContainer) dataContainer.classList.add('hidden');
    if (placeholder) {
      placeholder.classList.remove('hidden');
      placeholder.innerHTML = `
        <div class="text-center py-12">
          <div class="text-6xl mb-4">❌</div>
          <h4 class="text-xl font-medium text-gray-900 mb-2">
            Error Loading Cycle Time Data
          </h4>
          <p class="text-gray-600 mb-6">
            ${errorMessage}
          </p>
          <button 
            onclick="loadCycleTimeForCurrentBoard()"
            class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      `;
    }
  }
  
  /**
   * Displays probability table (reused from KanbanAnalytics)
   * Following Clean Code: Code reuse, single responsibility
   */
  function displayProbabilityTable(probabilityData: any, chartContainer: Element) {
    if (!probabilityData) return;
    
    console.log('[ScrumAnalytics] Displaying probability table');
    
    const tableBody = chartContainer.querySelector('#probability-table-body');
    if (!tableBody) {
      console.error('[ScrumAnalytics] Probability table body not found');
      return;
    }
    
    // Clear existing rows
    tableBody.innerHTML = '';
    
    // Generate table rows
    probabilityData.dayRanges.forEach((range: any) => {
      const row = document.createElement('tr');
      row.className = `probability-row ${range.isRecommended ? 'recommended-range' : ''}`;
      
      // Determine confidence level classes
      let confidenceClasses = 'bg-red-50 text-red-800'; // < 50%
      if (range.confidence >= 75) {
        confidenceClasses = 'bg-green-50 text-green-800'; // >= 75%
      } else if (range.confidence >= 50) {
        confidenceClasses = 'bg-yellow-50 text-yellow-800'; // 50-74%
      }
      
      row.innerHTML = `
        <td class="border border-gray-200 px-4 py-3 text-sm font-medium text-gray-900 w-20">
          ${range.range}
        </td>
        <td class="border border-gray-200 px-3 py-3 text-sm text-gray-700 w-20 text-center">
          <span class="text-blue-600 font-medium">${range.count}</span>
        </td>
        <td class="border border-gray-200 px-3 py-3 text-sm text-gray-700 w-30">
          ${range.probability}%
        </td>
        <td class="border border-gray-200 px-3 py-3 text-sm confidence-cell transition-all duration-300 ${confidenceClasses} w-30">
          ${range.confidence}%
        </td>
      `;
      
      // Add tooltip for recommended ranges
      if (range.isRecommended) {
        row.title = `Recommended range: ${range.count} issues completed in this timeframe`;
      }
      
      tableBody.appendChild(row);
    });
    
    // Update recommendations
    const recommendationText = chartContainer.querySelector('#recommendation-text');
    if (recommendationText && probabilityData.recommendations) {
      const { minDays, maxDays, confidenceLevel } = probabilityData.recommendations;
      recommendationText.textContent = 
        `For optimal planning, expect ${minDays}-${maxDays} days for completion with ${confidenceLevel}% confidence. Highlighted ranges show the most common completion times.`;
    }
  }
  
  /**
   * Displays issues details table (reused from KanbanAnalytics)
   * Following Clean Code: Code reuse, single responsibility
   */
  function displayIssuesDetails(issuesDetails: any[]) {
    console.log('[ScrumAnalytics] Displaying issues details table');
    
    // Find containers
    const contentContainer = document.querySelector('#issues-details-content');
    const placeholderContainer = document.querySelector('#issues-details-placeholder');
    const emptyContainer = document.querySelector('#issues-details-empty');
    const tableBody = document.querySelector('#issues-details-table-body');
    const issuesCount = document.querySelector('#issues-count');
    
    if (!contentContainer || !placeholderContainer || !emptyContainer || !tableBody || !issuesCount) {
      console.error('[ScrumAnalytics] Issues details containers not found');
      return;
    }
    
    // Hide placeholder
    placeholderContainer.classList.add('hidden');
    
    if (issuesDetails.length === 0) {
      // Show empty state
      contentContainer.classList.add('hidden');
      emptyContainer.classList.remove('hidden');
      return;
    }
    
    // Show content and update count
    contentContainer.classList.remove('hidden');
    emptyContainer.classList.add('hidden');
    issuesCount.textContent = issuesDetails.length.toString();
    
    // Clear existing table rows
    tableBody.innerHTML = '';
    
    // Populate table rows
    issuesDetails.forEach(issue => {
      const row = document.createElement('tr');
      row.className = 'hover:bg-gray-50';
      
      // Get status category color
      const getStatusCategoryColor = (categoryName: string) => {
        switch (categoryName) {
          case 'To Do': return 'bg-gray-100 text-gray-800';
          case 'In Progress': return 'bg-blue-100 text-blue-800';
          case 'Done': return 'bg-green-100 text-green-800';
          default: return 'bg-gray-100 text-gray-800';
        }
      };
      
      const statusColorClass = getStatusCategoryColor(issue.status.statusCategory.name);
      const cycleTimeDisplay = issue.cycleTimeDays ? `${issue.cycleTimeDays.toFixed(1)} days` : '-';
      
      row.innerHTML = `
        <td class="border border-gray-200 px-4 py-3">
          <div class="flex items-center">
            <img src="${issue.issueType.iconUrl}" alt="${issue.issueType.name}" class="w-4 h-4 mr-2" />
            <span class="text-sm font-medium text-gray-900">${issue.issueType.name}</span>
          </div>
        </td>
        <td class="border border-gray-200 px-4 py-3">
          <a href="${issue.jiraUrl}" target="_blank" rel="noopener noreferrer" 
             class="text-blue-600 hover:text-blue-800 font-medium text-sm hover:underline">
            ${issue.key}
          </a>
        </td>
        <td class="border border-gray-200 px-4 py-3">
          <div class="text-sm text-gray-900 max-w-md truncate" title="${issue.summary}">
            ${issue.summary}
          </div>
        </td>
        <td class="border border-gray-200 px-4 py-3">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColorClass}">
            ${issue.status.name}
          </span>
        </td>
        <td class="border border-gray-200 px-4 py-3">
          <span class="text-sm font-medium text-gray-900">${cycleTimeDisplay}</span>
        </td>
      `;
      
      tableBody.appendChild(row);
    });
    
    console.log(`[ScrumAnalytics] Issues details table populated with ${issuesDetails.length} issues`);
  }
  
  /**
   * Initializes tab functionality on page load
   * Following Clean Code: Setup function, clear intent
   */
  function initializeTabs() {
    // Restore saved tab preference
    const savedTab = localStorage.getItem('scrumAnalyticsActiveTab') || 'velocity';
    switchTab(savedTab);
    
    // Setup apply filters button for cycle time tab
    const applyButton = document.getElementById('apply-filters-button');
    if (applyButton) {
      applyButton.addEventListener('click', () => {
        if (currentBoardId) {
          loadCycleTimeForCurrentBoard();
        }
      });
    }
  }
  
  // Make functions available globally
  (window as any).switchTab = switchTab;
  (window as any).loadCycleTimeForCurrentBoard = loadCycleTimeForCurrentBoard;
  
  /**
   * Resets loading state when board changes
   * Following Clean Code: State management, clear intent
   */
  function resetLoadingState(boardId: string | null) {
    if (boardId !== currentBoardId) {
      velocityDataLoaded = false;
      cycleTimeDataLoaded = false;
      currentBoardId = boardId;
      console.log('[ScrumAnalytics] Loading state reset for board:', boardId);
    }
  }

  // Export functions for parent component access
  (window as any).scrumAnalytics = {
    loadScrumAnalytics,
    updateVelocityChart,
    updateSprintMetrics,
    updateVelocityTrends,
    showLoading,
    hideLoading,
    switchTab,
    loadCycleTimeForCurrentBoard,
    resetLoadingState
  };
  
  // Initialize tabs when DOM is ready
  document.addEventListener('DOMContentLoaded', initializeTabs);
</script>

<!-- Import Kanban events and functionality for Cycle Time tab -->
<script>
  import { kanbanState, type KanbanAnalyticsData } from '../../lib/events/kanban-events';
  
  // Import Kanban analytics functionality to support cycle time tab
  // This ensures that period selectors and other kanban components work correctly
  let currentPeriod: string = 'last-15-days';
  let currentCustomRange: { start: string; end: string } | null = null;
  let currentIssueTypes: string[] = [];
  
  /**
   * Handles period change events from PeriodSelector in cycle time tab
   * Following Clean Code: Event-driven architecture, clear intent
   */
  function handleCycleTimePeriodChange(event: CustomEvent) {
    const { period, customRange } = event.detail;
    
    if (!currentBoardId) return;
    
    console.log('[ScrumAnalytics] Cycle time period changed:', { period, customRange });
    
    // Update current state but don't reload automatically
    currentPeriod = period;
    currentCustomRange = customRange;
  }
  
  /**
   * Loads issue types for cycle time selector
   * Following Clean Code: Express intent, async operation
   */
  async function loadIssueTypesForCycleTime(boardId: string) {
    const issueTypeSelector = (window as any).issueTypeSelector;
    if (issueTypeSelector) {
      await issueTypeSelector.loadIssueTypes(boardId);
      // Get all issue types as default selection
      currentIssueTypes = issueTypeSelector.getSelectedIssueTypes();
    }
  }
  
  /**
   * Updates filter components state for cycle time tab
   * Following Clean Code: Single responsibility, state management
   */
  function updateCycleTimeFiltersState(boardId: string | null, loading: boolean = false) {
    // Update period selector
    const periodSelect = document.getElementById('period-select') as HTMLSelectElement;
    const customStartInput = document.getElementById('custom-start') as HTMLInputElement;
    const customEndInput = document.getElementById('custom-end') as HTMLInputElement;
    const applyButton = document.getElementById('apply-filters-button') as HTMLButtonElement;
    
    const shouldDisable = !boardId || loading;
    
    // Update all period selector elements
    [periodSelect, customStartInput, customEndInput, applyButton].forEach(element => {
      if (element) {
        element.disabled = shouldDisable;
      }
    });
    
    // Update issue type selector
    const issueTypeSelector = (window as any).issueTypeSelector;
    if (issueTypeSelector) {
      issueTypeSelector.updateIssueTypeSelectorState(boardId, loading);
    }
    
    console.log(`[ScrumAnalytics] Cycle time filters ${shouldDisable ? 'disabled' : 'enabled'} (boardId: ${boardId}, loading: ${loading})`);
  }
  
  // Setup event listeners for cycle time functionality
  document.addEventListener('periodChanged', handleCycleTimePeriodChange as EventListener);
  
  // Make cycle time functions available globally for the cycle time tab
  (window as any).scrumAnalyticsCycleTime = {
    loadIssueTypesForCycleTime,
    updateCycleTimeFiltersState
  };
</script>
