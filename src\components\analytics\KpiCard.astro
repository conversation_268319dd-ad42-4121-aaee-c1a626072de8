---
/**
 * KPI Card component for dashboard metrics
 * Following Clean Code: Express intent, small and focused
 */

export interface Props {
  title: string;
  value: number;
  subtitle?: string;
  suffix?: string;
  trend?: 'up' | 'down' | 'neutral';
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'gray';
}

const { 
  title, 
  value, 
  subtitle, 
  suffix = '',
  trend = 'neutral',
  color = 'blue' 
} = Astro.props;

// Color mappings following DRY principle - only border colors, white background
const borderColorClasses = {
  blue: 'border-blue-500',
  green: 'border-green-500',
  yellow: 'border-yellow-500',
  red: 'border-red-500',
  gray: 'border-gray-500'
};

const textColorClasses = {
  blue: 'text-blue-600',
  green: 'text-green-600',
  yellow: 'text-yellow-600',
  red: 'text-red-600',
  gray: 'text-gray-600'
};

const trendIcons = {
  up: '↗',
  down: '↘',
  neutral: '→'
};

const trendColors = {
  up: 'text-green-500',
  down: 'text-red-500',
  neutral: 'text-gray-500'
};
---

<div class={`bg-white border-l-4 rounded-lg shadow-sm p-6 ${borderColorClasses[color]}`}>
  <div class="flex items-center justify-between">
    <div>
      <p class="text-sm font-medium text-gray-600">{title}</p>
      <p class={`text-3xl font-bold ${textColorClasses[color]}`}>
        {value.toLocaleString()}{suffix}
      </p>
      {subtitle && (
        <p class="text-sm text-gray-500 mt-1">{subtitle}</p>
      )}
    </div>
    {trend && (
      <div class={`text-2xl ${trendColors[trend]}`}>
        {trendIcons[trend]}
      </div>
    )}
  </div>
</div>
