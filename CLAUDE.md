# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Guidelines

- Prima di proseguire con le implementazioni, fai sempre un piano di sviluppo e fallo approvare da me
- Se ti mancano delle informazioni, chiedimele senza proseguire e fare supposizioni
- Quando trovi discrepanze tra test e implementazione, fermati e chiedimi sempre quale approccio preferisco (modificare test vs implementazione) prima di procedere
- Implementa solo quello che ti viene richiesto, senza aggiungere funzionalità extra
- Non essere accondiscendente, se pensi che esistea una soluzione migliore, dimmelo apertamente spiegandomi i motivi delle tue scelte.
- Evita sempre di fare test e implementazioni nello stesso momento: se stai modificando i file del progetto, non modificare i file di test e viceversa
- Prima di modificare qualsiasi file (test o implementazione), presenta sempre le opzioni disponibili e aspetta la mia approvazione
- Consulta sempre i file nella cartella `.claude/` prima di generare codice per mantenere coerenza con le scelte di progetto
- Aggiorna sempre il file `.claude/requirements.md` con le novità più rilevanti sulle funzionalità del progetto

## Development Commands

### Development Server
- `npm run dev` - Start Astro development server (runs on http://localhost:4321)
- `npm run build` - Build for production
- `npm run preview` - Preview production build locally
- `npm start` - Start production server from dist

### Type Checking and Testing  
- `npm run type-check` - Run Astro TypeScript checking
- `npm test` - Run Jest tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Generate test coverage report

### Database Operations
- `npm run db:generate` - Generate Drizzle migrations from schema changes
- `npm run db:migrate` - Apply migrations to database
- `npm run db:push` - Push schema changes directly to database (used in production)
- `npm run db:studio` - Open Drizzle Studio for database exploration
- `npm run db:setup:turso` - Setup Turso database connection

### Database Testing
- `npm run test:db` - Test database integration
- `npm run test:db:dev` - Test database integration against local dev server

## Project Architecture

This is a **Jira Analytics Dashboard** built with Astro, TypeScript, and Tailwind CSS that calculates team velocity and Kanban metrics using Clean Architecture principles.

### High-Level Architecture

**Domain Layer** (`src/lib/`):
- `velocity/` - Core velocity calculation logic and validation algorithms
- `analytics/` - Analytics calculations for Scrum and Kanban metrics  
- `jira/` - Jira API client, types, and domain models
- `kanban/` - Cycle time calculation and Kanban-specific logic
- `database/` - Database layer with Clean Architecture patterns

**Application Layer** (`src/pages/api/`):
- API endpoints that orchestrate domain services
- Handles HTTP requests and coordinates between layers
- Database persistence and caching logic

**Infrastructure Layer**:
- MCP Atlassian client (`src/lib/mcp/atlassian.ts`) for Jira API integration
- Database repositories using Drizzle ORM with Turso (LibSQL)
- Caching utilities and external service integrations

**Presentation Layer** (`src/components/` and `src/pages/`):
- Astro components for UI (`.astro` files)
- Reactive components for dashboards and analytics
- Tailwind CSS for styling

### Key Components

**Velocity Calculation System**:
- Advanced validation logic that checks when issues actually reached "Done" columns
- Batch processing for performance optimization
- Support for both API-based and database-cached calculations
- Story points validation and completion date extraction

**Database Architecture**:
- Clean Architecture with Repository pattern
- Factory pattern for database connections (Turso/LibSQL)
- Domain entities with type safety
- Migration system with Drizzle Kit

**Jira Integration**:
- MCP (Model Context Protocol) client for Atlassian API
- Board configuration API for validation
- Issue tracking with status transitions
- Sprint and changelog data processing

### Design Patterns Used

- **Factory Pattern**: Database connections and repository creation
- **Repository Pattern**: Data access abstraction
- **Strategy Pattern**: Different validation algorithms for velocity
- **Observer Pattern**: Real-time progress reporting during calculations
- **Command Pattern**: Batch operations and database persistence

### Environment Configuration

Required environment variables (create `.env.local`):
```env
JIRA_BASE_URL=https://your-domain.atlassian.net
JIRA_EMAIL=<EMAIL>  
JIRA_API_TOKEN=your-jira-api-token
JIRA_PROJECT_KEY=PROJECT

# Database (Turso)
TURSO_DATABASE_URL=your-turso-url
TURSO_AUTH_TOKEN=your-turso-token
```

### Testing Strategy

- Jest for unit tests with TypeScript support
- Test files located in `__tests__/` directories within each module
- Database integration tests with test fixtures
- Mocking strategy for external API calls

### Performance Considerations

- Batch API validation to minimize Jira API calls
- Database-first loading strategy (prefer cached data over API calls)
- Progress callbacks for long-running operations
- Caching layer for expensive calculations

### Development Guidelines

The codebase follows Clean Code and Clean Architecture principles:
- Single Responsibility Principle throughout
- Express intent with clear naming
- Immutable data structures where possible
- Error handling with proper context
- Dependency injection for testability

All code is written in **TypeScript** with strict type checking enabled. Use the existing patterns for new features and maintain consistency with the established architecture.