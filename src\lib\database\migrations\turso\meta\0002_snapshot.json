{"version": "6", "dialect": "sqlite", "id": "b2eedbed-b54b-460a-a4ca-fffb2ff84ee9", "prevId": "edf74e53-41e5-4ff0-b911-f71389088395", "tables": {"board_configurations": {"name": "board_configurations", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "project_key": {"name": "project_key", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "done_status_ids": {"name": "done_status_ids", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "story_points_field": {"name": "story_points_field", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "custom_fields": {"name": "custom_fields", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_fetched": {"name": "last_fetched", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(datetime('now'))"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(datetime('now'))"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "board_metrics": {"name": "board_metrics", "columns": {"board_id": {"name": "board_id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "board_name": {"name": "board_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "average_velocity": {"name": "average_velocity", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "predictability": {"name": "predictability", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "trend": {"name": "trend", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "sprints_analyzed": {"name": "sprints_analyzed", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_calculated": {"name": "last_calculated", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(datetime('now'))"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "closed_sprints": {"name": "closed_sprints", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "board_id": {"name": "board_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "start_date": {"name": "start_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "end_date": {"name": "end_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "complete_date": {"name": "complete_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "goal": {"name": "goal", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "origin_board_id": {"name": "origin_board_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "velocity_data": {"name": "velocity_data", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "issues_data": {"name": "issues_data", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "metrics_data": {"name": "metrics_data", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(datetime('now'))"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(datetime('now'))"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "sprint_issues": {"name": "sprint_issues", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "sprint_id": {"name": "sprint_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "issue_key": {"name": "issue_key", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "issue_id": {"name": "issue_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "summary": {"name": "summary", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "issue_type": {"name": "issue_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "story_points": {"name": "story_points", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "assignee": {"name": "assignee", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created": {"name": "created", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated": {"name": "updated", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "resolved": {"name": "resolved", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "completion_date": {"name": "completion_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "custom_fields": {"name": "custom_fields", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status_history": {"name": "status_history", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(datetime('now'))"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(datetime('now'))"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}