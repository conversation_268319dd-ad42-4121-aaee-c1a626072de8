---
/**
 * Loading spinner component
 * Following Clean Code: Single responsibility, reusable
 */

export interface Props {
  size?: 'sm' | 'md' | 'lg';
  message?: string;
}

const { size = 'md', message = 'Loading...' } = Astro.props;

const sizeClasses = {
  sm: 'w-4 h-4',
  md: 'w-8 h-8', 
  lg: 'w-12 h-12'
};
---

<div class="flex flex-col items-center justify-center p-4">
  <div class={`animate-spin rounded-full border-2 border-gray-200 border-t-blue-600 ${sizeClasses[size]}`}></div>
  {message && (
    <p class="mt-2 text-sm text-gray-600">{message}</p>
  )}
</div>
