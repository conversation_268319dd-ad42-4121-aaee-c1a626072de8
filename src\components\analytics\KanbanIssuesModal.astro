---
/**
 * <PERSON><PERSON><PERSON> Issues Modal component
 * Simple standalone modal without dependencies
 */
---

<!-- Kanban Issues Modal - Standalone -->
<div 
  id="kanban-issues-modal" 
  class="fixed inset-0 z-50 flex items-center justify-center p-4 hidden"
  style="background-color: rgba(0, 0, 0, 0.5);"
>
  <div 
    class="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden"
    role="dialog"
    aria-modal="true"
    aria-labelledby="kanban-modal-title"
  >
    <!-- Modal Header -->
    <div class="flex items-center justify-between p-6 border-b border-gray-200">
      <h2 id="kanban-modal-title" class="text-xl font-semibold text-gray-900">
        Cycle Time Issues
      </h2>
      <button
        type="button"
        id="kanban-modal-close-button"
        class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
        aria-label="Close modal"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
        </svg>
      </button>
    </div>
    
    <!-- Modal Content -->
    <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
      <div id="kanban-issues-content">
        <!-- Loading State -->
        <div id="cycle-time-loading" class="hidden">
          <div class="flex items-center justify-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span class="ml-3 text-gray-600">Loading cycle time issues...</span>
          </div>
        </div>
        
        <!-- Error State -->
        <div id="cycle-time-error" class="hidden">
          <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
              <div class="text-red-400">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-red-800" id="cycle-time-error-message">
                  Failed to load cycle time issues
                </p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Issues Table -->
        <div id="cycle-time-table-container" class="hidden">
          <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
            <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
              <h4 class="text-lg font-medium text-gray-900">Cycle Time Issues</h4>
              <p class="text-sm text-gray-600" id="cycle-time-issues-count">Loading...</p>
            </div>
            
            <!-- Desktop Table -->
            <div class="hidden md:block overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Key
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Summary
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Opened Date
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Last Done Date
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Cycle Time (Days)
                    </th>
                  </tr>
                </thead>
                <tbody id="cycle-time-table-body" class="bg-white divide-y divide-gray-200">
                  <!-- Issues will be populated here by JavaScript -->
                </tbody>
              </table>
            </div>
            
            <!-- Mobile Cards -->
            <div class="md:hidden" id="cycle-time-mobile-container">
              <!-- Mobile issue cards will be populated here -->
            </div>
          </div>
        </div>
        
        <!-- Empty State -->
        <div id="cycle-time-empty" class="hidden">
          <div class="text-center py-8">
            <div class="text-gray-400 mb-4">
              <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
            </div>
            <p class="text-gray-500">No issues found in this cycle time range</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  import { kanbanState, KANBAN_EVENTS, type KanbanAnalyticsData, type CycleTimeRange } from '../../lib/events/kanban-events';

  /**
   * Interface for issue details with cycle time information
   */
  interface CycleTimeIssue {
    key: string;
    summary: string;
    issueType: {
      name: string;
      iconUrl: string;
    };
    status: {
      name: string;
      statusCategory: {
        name: string;
      };
    };
    jiraUrl: string;
    cycleTimeDays: number;
    openedDate: string;
    lastDoneDate: string;
  }

  let currentCycleTimeRange: CycleTimeRange | null = null;

  /**
   * Shows the kanban issues modal with cycle time data
   */
  function showKanbanIssuesModal(cycleTimeRange?: CycleTimeRange) {
    const modal = document.getElementById('kanban-issues-modal');
    if (modal) {
      modal.classList.remove('hidden');
      modal.classList.add('flex');
      
      // Prevent body scroll
      document.body.style.overflow = 'hidden';
      
      // Update modal title based on cycle time range
      const title = document.getElementById('kanban-modal-title');
      if (title && cycleTimeRange) {
        title.textContent = `Cycle Time Issues (${cycleTimeRange.minDays}-${cycleTimeRange.maxDays} days)`;
      }
      
      // Load cycle time issues if range provided
      if (cycleTimeRange) {
        currentCycleTimeRange = cycleTimeRange;
        loadCycleTimeIssues(cycleTimeRange);
      }
    }
  }

  /**
   * Closes the kanban issues modal
   */
  function closeKanbanIssuesModal() {
    const modal = document.getElementById('kanban-issues-modal');
    if (modal) {
      modal.classList.add('hidden');
      modal.classList.remove('flex');
      
      // Restore body scroll
      document.body.style.overflow = '';
      
      // Reset current range
      currentCycleTimeRange = null;
      
      // Hide all content sections
      hideAllCycleTimeStates();
    }
  }

  /**
   * Loads and displays cycle time issues for the given range
   */
  async function loadCycleTimeIssues(cycleTimeRange: CycleTimeRange) {
    console.log('[KanbanIssuesModal] Loading cycle time issues for range:', cycleTimeRange);
    showCycleTimeLoading();
    
    try {
      // Get issues from the kanban state manager
      const kanbanData = kanbanState.getCurrentData();
      console.log('[KanbanIssuesModal] Kanban data available:', !!kanbanData);
      
      if (!kanbanData || !kanbanData.issuesDetails) {
        console.error('[KanbanIssuesModal] No kanban analytics data available');
        showCycleTimeError('No kanban analytics data available. Please load analytics first.');
        return;
      }
      
      console.log('[KanbanIssuesModal] Total issues available:', kanbanData.issuesDetails.length);
      
      // Filter issues by cycle time range
      const filteredIssues = kanbanData.issuesDetails.filter((issue) => {
        const cycleTime = issue.cycleTimeDays;
        if (cycleTime === undefined || cycleTime === null) {
          return false;
        }
        return cycleTime >= cycleTimeRange.minDays && cycleTime < cycleTimeRange.maxDays;
      });
      
      console.log('[KanbanIssuesModal] Filtered issues count:', filteredIssues.length);
      
      if (filteredIssues.length === 0) {
        showCycleTimeEmpty();
        return;
      }
      
      showCycleTimeIssues(filteredIssues);
      
    } catch (error) {
      console.error('[KanbanIssuesModal] Error loading cycle time issues:', error);
      showCycleTimeError('An unexpected error occurred');
    }
  }

  /**
   * Shows loading state for cycle time issues
   */
  function showCycleTimeLoading() {
    hideAllCycleTimeStates();
    document.getElementById('cycle-time-loading')?.classList.remove('hidden');
  }

  /**
   * Shows error state with message
   */
  function showCycleTimeError(message: string) {
    const errorElement = document.getElementById('cycle-time-error-message');
    if (errorElement) {
      errorElement.textContent = message;
    }
    
    hideAllCycleTimeStates();
    document.getElementById('cycle-time-error')?.classList.remove('hidden');
  }

  /**
   * Shows empty state
   */
  function showCycleTimeEmpty() {
    hideAllCycleTimeStates();
    document.getElementById('cycle-time-empty')?.classList.remove('hidden');
  }

  /**
   * Hides all cycle time state elements
   */
  function hideAllCycleTimeStates() {
    document.getElementById('cycle-time-loading')?.classList.add('hidden');
    document.getElementById('cycle-time-error')?.classList.add('hidden');
    document.getElementById('cycle-time-table-container')?.classList.add('hidden');
    document.getElementById('cycle-time-empty')?.classList.add('hidden');
  }

  /**
   * Shows cycle time issues table with data
   */
  function showCycleTimeIssues(issues: CycleTimeIssue[]) {
    const countElement = document.getElementById('cycle-time-issues-count');
    if (countElement) {
      countElement.textContent = `${issues.length} issues found`;
    }
    
    renderCycleTimeTable(issues);
    renderCycleTimeMobile(issues);
    
    hideAllCycleTimeStates();
    document.getElementById('cycle-time-table-container')?.classList.remove('hidden');
  }

  /**
   * Formats date for display
   */
  function formatDateForDisplay(dateString: string): string {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return 'Invalid date';
    }
  }

  /**
   * Gets issue type color based on type name
   */
  function getIssueTypeColor(issueTypeName: string): string {
    const typeName = issueTypeName.toLowerCase();
    
    if (typeName.includes('story')) return 'bg-green-100 text-green-800';
    if (typeName.includes('bug')) return 'bg-red-100 text-red-800';
    if (typeName.includes('task')) return 'bg-blue-100 text-blue-800';
    if (typeName.includes('epic')) return 'bg-purple-100 text-purple-800';
    if (typeName.includes('improvement')) return 'bg-yellow-100 text-yellow-800';
    if (typeName.includes('sub-task') || typeName.includes('subtask')) return 'bg-gray-100 text-gray-800';
    
    return 'bg-gray-100 text-gray-800';
  }

  /**
   * Gets status color based on status category
   */
  function getStatusColor(statusCategory: string): string {
    switch (statusCategory.toLowerCase()) {
      case 'to do':
        return 'bg-gray-100 text-gray-800';
      case 'in progress':
        return 'bg-blue-100 text-blue-800';
      case 'done':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  /**
   * Renders cycle time issues table for desktop
   */
  function renderCycleTimeTable(issues: CycleTimeIssue[]) {
    const tbody = document.getElementById('cycle-time-table-body');
    if (!tbody) return;
    
    tbody.innerHTML = issues.map(issue => `
      <tr class="hover:bg-gray-50 transition-colors duration-150">
        <td class="px-6 py-4 whitespace-nowrap">
          <a 
            href="${issue.jiraUrl}" 
            target="_blank" 
            rel="noopener noreferrer"
            class="text-blue-600 hover:text-blue-800 font-medium"
          >
            ${issue.key}
          </a>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="px-2 py-1 text-xs font-medium rounded-full ${getIssueTypeColor(issue.issueType.name)}">
            ${issue.issueType.name}
          </span>
        </td>
        <td class="px-6 py-4">
          <div class="text-sm text-gray-900 max-w-xs truncate" title="${issue.summary}">
            ${issue.summary}
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(issue.status.statusCategory.name)}">
            ${issue.status.name}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${formatDateForDisplay(issue.openedDate)}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${formatDateForDisplay(issue.lastDoneDate)}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <span class="text-sm font-medium text-gray-900">
            ${issue.cycleTimeDays.toFixed(1)}
          </span>
        </td>
      </tr>
    `).join('');
  }

  /**
   * Renders cycle time issues cards for mobile
   */
  function renderCycleTimeMobile(issues: CycleTimeIssue[]) {
    const container = document.getElementById('cycle-time-mobile-container');
    if (!container) return;
    
    container.innerHTML = issues.map(issue => `
      <div class="p-4 border-b border-gray-200 last:border-b-0">
        <div class="flex items-start justify-between mb-2">
          <a 
            href="${issue.jiraUrl}" 
            target="_blank" 
            rel="noopener noreferrer"
            class="text-blue-600 hover:text-blue-800 font-medium"
          >
            ${issue.key}
          </a>
          <span class="px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(issue.status.statusCategory.name)}">
            ${issue.status.name}
          </span>
        </div>
        <div class="mb-2">
          <span class="px-2 py-1 text-xs font-medium rounded-full ${getIssueTypeColor(issue.issueType.name)}">
            ${issue.issueType.name}
          </span>
        </div>
        <div class="text-sm text-gray-900 mb-2">${issue.summary}</div>
        <div class="grid grid-cols-2 gap-2 text-xs text-gray-500">
          <div>
            <span class="font-medium">Opened:</span><br>
            ${formatDateForDisplay(issue.openedDate)}
          </div>
          <div>
            <span class="font-medium">Done:</span><br>
            ${formatDateForDisplay(issue.lastDoneDate)}
          </div>
        </div>
        <div class="mt-2 text-center">
          <span class="text-sm font-medium text-gray-900">
            Cycle Time: ${issue.cycleTimeDays.toFixed(1)} days
          </span>
        </div>
      </div>
    `).join('');
  }
  
  // Event Listeners Setup
  // Following Clean Code: Event-driven architecture, proper cleanup
  
  /**
   * Handles show modal events
   */
  function handleShowModalEvent(event: CustomEvent) {
    const { cycleTimeRange } = event.detail;
    showKanbanIssuesModal(cycleTimeRange);
  }
  
  /**
   * Handles close modal events
   */
  function handleCloseModalEvent() {
    closeKanbanIssuesModal();
  }
  
  // Setup event listeners when DOM is ready
  document.addEventListener('DOMContentLoaded', () => {
    // Listen for modal events
    kanbanState.addEventListener(KANBAN_EVENTS.SHOW_ISSUES_MODAL, handleShowModalEvent as EventListener);
    kanbanState.addEventListener(KANBAN_EVENTS.CLOSE_ISSUES_MODAL, handleCloseModalEvent as EventListener);
    
    // Setup close button listener
    const closeButton = document.getElementById('kanban-modal-close-button');
    if (closeButton) {
      closeButton.addEventListener('click', () => {
        kanbanState.closeIssuesModal();
      });
    }
    
    // Setup click outside to close
    const modalOverlay = document.getElementById('kanban-issues-modal');
    if (modalOverlay) {
      modalOverlay.addEventListener('click', (event) => {
        // Only close if clicking on the overlay itself, not on the modal content
        if (event.target === modalOverlay) {
          kanbanState.closeIssuesModal();
        }
      });
    }
    
    // Setup ESC key to close
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        const modal = document.getElementById('kanban-issues-modal');
        if (modal && !modal.classList.contains('hidden')) {
          kanbanState.closeIssuesModal();
        }
      }
    });
  });
  
  // Cleanup event listeners when component is destroyed
  // Note: In a full framework this would be in a cleanup lifecycle method
  window.addEventListener('beforeunload', () => {
    kanbanState.removeEventListener(KANBAN_EVENTS.SHOW_ISSUES_MODAL, handleShowModalEvent as EventListener);
    kanbanState.removeEventListener(KANBAN_EVENTS.CLOSE_ISSUES_MODAL, handleCloseModalEvent as EventListener);
  });
</script>