---
/**
 * Enhanced Velocity Progress Loader for Multi-Stage Loading
 * Following Clean Code: Single responsibility, clear state management
 * Strategy 5: Multi-Stage Loading - UI Component
 */

export interface Props {
  /** Board ID being loaded */
  boardId: string;
  /** Current loading stage */
  stage: 'quick' | 'batch' | 'complete' | 'error';
  /** Progress percentage (0-100) */
  progress: number;
  /** Current stage message */
  message: string;
  /** Number of sprints loaded so far */
  sprintsLoaded?: number;
  /** Total sprints available */
  totalSprints?: number;
  /** Enable cancel functionality */
  allowCancel?: boolean;
  /** Cancel callback */
  onCancel?: () => void;
}

const { 
  boardId,
  stage = 'quick',
  progress = 0,
  message = 'Loading velocity data...',
  sprintsLoaded = 0,
  totalSprints = 0,
  allowCancel = false,
  onCancel
} = Astro.props;

// Stage configurations for visual representation
const stageConfig = {
  quick: {
    color: 'bg-blue-500',
    icon: '⚡',
    name: 'Quick Load',
    description: 'Loading recent sprints for immediate insights'
  },
  batch: {
    color: 'bg-indigo-500',
    icon: '📊',
    name: 'Historical Data',
    description: 'Loading comprehensive sprint history'
  },
  complete: {
    color: 'bg-green-500',
    icon: '✅',
    name: 'Complete',
    description: 'All available data loaded successfully'
  },
  error: {
    color: 'bg-red-500',
    icon: '❌',
    name: 'Error',
    description: 'Failed to load velocity data'
  }
};

const currentStage = stageConfig[stage];
const progressClamped = Math.max(0, Math.min(100, progress));
---

<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 max-w-md mx-auto">
  <!-- Header -->
  <div class="flex items-center justify-between mb-4">
    <div class="flex items-center space-x-2">
      <span class="text-2xl">{currentStage.icon}</span>
      <div>
        <h3 class="text-lg font-semibold text-gray-900">{currentStage.name}</h3>
        <p class="text-sm text-gray-600">Board {boardId}</p>
      </div>
    </div>
    
    {allowCancel && onCancel && (
      <button
        type="button"
        class="text-gray-400 hover:text-gray-600 transition-colors p-1"
        onclick={`if(window.velocityLoader) window.velocityLoader.cancel()`}
        title="Cancel loading"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    )}
  </div>

  <!-- Progress Bar -->
  <div class="mb-4">
    <div class="flex justify-between text-sm text-gray-600 mb-2">
      <span>Progress</span>
      <span>{progressClamped}%</span>
    </div>
    <div class="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
      <div 
        class={`h-full ${currentStage.color} transition-all duration-500 ease-out`}
        style={`width: ${progressClamped}%`}
      >
      </div>
    </div>
  </div>

  <!-- Stage Description -->
  <p class="text-sm text-gray-600 mb-3">
    {currentStage.description}
  </p>

  <!-- Current Message -->
  <div class="bg-gray-50 rounded-md p-3 mb-4">
    <p class="text-sm font-medium text-gray-700">{message}</p>
  </div>

  <!-- Sprint Counter (if available) -->
  {totalSprints > 0 && (
    <div class="flex justify-between text-xs text-gray-500">
      <span>Sprints loaded: {sprintsLoaded}</span>
      <span>Total available: {totalSprints}</span>
    </div>
  )}

  <!-- Stage Indicators -->
  <div class="mt-4 flex items-center justify-center space-x-2">
    <div class={`w-3 h-3 rounded-full transition-colors ${
      ['quick', 'batch', 'complete'].includes(stage) ? 'bg-blue-500' : 'bg-gray-300'
    }`} title="Quick Load"></div>
    <div class={`w-3 h-3 rounded-full transition-colors ${
      ['batch', 'complete'].includes(stage) ? 'bg-indigo-500' : 'bg-gray-300'
    }`} title="Historical Data"></div>
    <div class={`w-3 h-3 rounded-full transition-colors ${
      stage === 'complete' ? 'bg-green-500' : 'bg-gray-300'
    }`} title="Complete"></div>
  </div>

  <!-- Error State -->
  {stage === 'error' && (
    <div class="mt-4 pt-4 border-t border-gray-200">
      <div class="flex items-center space-x-2">
        <button
          type="button"
          class="flex-1 bg-blue-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
          onclick="window.location.reload()"
        >
          Retry
        </button>
        <button
          type="button"
          class="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
          onclick="history.back()"
        >
          Go Back
        </button>
      </div>
    </div>
  )}
</div>

<!-- Loading Animation Styles -->
<style>
  @keyframes pulse-glow {
    0%, 100% {
      box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
    }
    50% {
      box-shadow: 0 0 15px rgba(59, 130, 246, 0.6);
    }
  }

  .bg-blue-500,
  .bg-indigo-500 {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  .bg-green-500,
  .bg-red-500 {
    animation: none;
  }

  /* Smooth progress bar animation */
  .h-full {
    transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }
</style>

<!-- Note: Progress updates will be handled by parent components through re-rendering --> 