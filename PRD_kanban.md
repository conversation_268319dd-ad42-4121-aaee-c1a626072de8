# Product Requirement Document (PRD)

## Titolo
**Estensione Metriche Kanban per Jira – Tool di Analisi Metriche Agile**

## Versione
1.0  
**Data:** 09/07/2025  
**Autore:** [Da compilare]

## 1. Scopo del Prodotto

Il prodotto consiste nell’estensione di un tool esistente per la raccolta e visualizzazione di metriche Agile, aggiungendo il supporto alle board Kanban di Jira. L’obiettivo è fornire agli utenti metriche avanzate e predittive (cycle time e percentili di completamento) sulle issue gestite tramite Kanban, con possibilità di filtrare e salvare configurazioni per analisi future.

## 2. Contesto e Motivazione

Attualmente il tool supporta solo metriche Scrum. I team che adottano Kanban necessitano di strumenti di analisi specifici per migliorare la previsione dei tempi di consegna e ottimizzare il flusso di lavoro. Questo sviluppo mira a colmare il gap, offrendo funzionalità di calcolo e visualizzazione dei cycle time e percentili di completamento, con filtri avanzati.

## 3. Stakeholder

- **Product Owner**
- **Team di sviluppo**
- **Team Kanban**
- **Agile Coach**
- **Project Manager**

## 4. Requisiti Funzionali

### 4.1 Selezione Board Kanban

- Il sistema deve permettere la selezione di una o più board Kanban di Jira tra quelle disponibili.
- Supporto completo a mapping dinamico colonna-stato per board con configurazioni personalizzate.

### 4.2 Raccolta e Gestione Issue

- Considerare solo le issue che hanno raggiunto la colonna “Done” (colonna di tipo “Done” secondo il mapping della board).
- Escludere automaticamente le issue che sono state successivamente riaperte dopo la chiusura.
- In caso di issue senza data di ingresso nella board, utilizzare la data di creazione della issue.

### 4.3 Calcolo Cycle Time

- Il cycle time di una issue è calcolato come la differenza tra:
    - Data di ingresso nella prima colonna della board (primo stato mappato sulla board).
    - Data di ingresso nell’ultima colonna “Done” (considerando solo l’ultimo ciclo di chiusura).
- Se la issue è stata riaperta e poi chiusa nuovamente, il cycle time si basa sull’ultimo ingresso in “Done”.

### 4.4 Persistenza Dati

- Salvare per ogni issue:
    - ID issue
    - Summary
    - Tipo
    - Stato finale
    - Data ingresso board (o data di creazione)
    - Data ingresso “Done” (ultimo ingresso)
    - Data di apertura (creazione)
    - Data di chiusura (ultimo ingresso in “Done”)
    - Cycle time
    - Assignee
    - Board di appartenenza
- Il salvataggio avviene periodicamente su tutte le issue (non incrementale).

### 4.5 Calcolo Metriche e Percentili

- Calcolare e visualizzare i seguenti percentili di completamento del cycle time per ogni board/segmento selezionato:
    - 50° percentile (mediana)
    - 85° percentile
    - 95° percentile
- I percentili non sono configurabili runtime.

### 4.6 Filtri Avanzati e Swimlane

- L’utente può selezionare più tipologie di issue contemporaneamente.
- Le swimlane sono definite esclusivamente tramite JQL.
- Il sistema deve salvare le configurazioni di filtro e swimlane per analisi/report futuri.

### 4.7 Visualizzazione Metriche

- Le metriche devono essere visualizzate in modo chiaro, efficace e coerente con lo stile grafico già in uso nel tool.
- Non è prevista l’esportazione o la generazione di report scaricabili.

### 4.8 Gestione Eccezioni e Logging

- In caso di dati mancanti (es. data ingresso board), utilizzare la data di creazione della issue.
- Loggare tutti gli errori di calcolo e le issue escluse dal calcolo delle metriche.

## 5. Requisiti Non Funzionali

- **Performance:** Il calcolo delle metriche deve essere rapido e non impattare negativamente l’esperienza utente.
- **Sicurezza:** Gestione sicura delle credenziali di accesso a Jira e dei dati sensibili.
- **Manutenibilità:** Il mapping colonne-stati e la gestione delle configurazioni devono essere facilmente estendibili.
- **Compatibilità:** Supporto garantito per tutte le versioni recenti di Jira Cloud e Server.

## 6. Vincoli

- Il sistema si integra esclusivamente con Jira (no altri strumenti di project management).
- Le swimlane sono sempre e solo definite tramite JQL.
- Nessuna funzione di esportazione prevista in questa fase.

## 7. User Stories (Esempi)

- **US1:** Come Product Owner, voglio selezionare una board Kanban di Jira per visualizzare i percentili di completamento delle issue, così da prevedere meglio i tempi di consegna.
- **US2:** Come Team Lead, voglio filtrare i dati per tipologia di issue e swimlane JQL, così da analizzare il flusso di lavoro per specifici tipi di task.
- **US3:** Come utente, voglio che il sistema escluda automaticamente le issue riaperte dopo la chiusura, così da avere metriche più affidabili.

## 8. Flusso Operativo

1. L’utente seleziona la board Kanban di interesse.
2. Il sistema recupera tutte le issue che hanno raggiunto la colonna “Done” e non sono state riaperte.
3. Per ciascuna issue, viene calcolato il cycle time secondo le regole definite.
4. L’utente può applicare filtri per tipologia di issue e swimlane JQL.
5. Il sistema visualizza i percentili richiesti in formato grafico/tabellare.
6. Le configurazioni di filtro possono essere salvate per utilizzi futuri.
7. Tutte le eccezioni e issue escluse vengono loggate.

## 9. Metriche di Successo

- Percentuale di team Kanban che utilizzano la nuova funzionalità.
- Accuratezza dei percentili di completamento rispetto ai dati storici.
- Feedback degli utenti sulla chiarezza e utilità delle visualizzazioni.
