---
/**
 * Sprint Issues Modal component
 * Following Clean Code: Single responsibility - display sprint issues only
 */

import Modal from '../ui/Modal.astro';
import type { SprintVelocity } from '../../lib/velocity/mock-calculator';


export interface Props {
  sprint: SprintVelocity | null;
  isOpen: boolean;
}

const { sprint, isOpen = false } = Astro.props;


---

<Modal 
  isOpen={isOpen}
  title={sprint ? `Sprint: ${sprint.sprint.name}` : 'Sprint Issues'}
  onClose="closeSprintModal"
  size="xl"
>
  <div id="sprint-issues-content">
    <!-- Sprint Header Information - Will be populated dynamically -->
    <div id="sprint-header-container" class="sprint-info mb-6 hidden">
      <div class="bg-gray-50 rounded-lg p-4 mb-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600" id="sprint-points">
              0/0
            </div>
            <div class="text-sm text-gray-600">Story Points</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600" id="sprint-completion">
              0%
            </div>
            <div class="text-sm text-gray-600">Completion Rate</div>
          </div>
          <div class="text-center">
            <div class="text-sm px-3 py-1 rounded-full inline-block bg-gray-100 text-gray-800" id="sprint-status">
              Unknown
            </div>
            <div class="text-sm text-gray-600 mt-1">Status</div>
          </div>
        </div>
        
        <div class="mt-4 text-center">
          <div class="text-sm text-gray-600" id="sprint-dates">
            📅 Loading...
          </div>
          <div class="text-sm text-gray-700 mt-2 hidden" id="sprint-goal-container">
            <strong>Goal:</strong> <span id="sprint-goal"></span>
          </div>
        </div>
      </div>
        
        <!-- Loading State -->
        <div id="issues-loading" class="hidden">
          <div class="flex items-center justify-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span class="ml-3 text-gray-600">Loading sprint issues...</span>
          </div>
        </div>
        
        <!-- Error State -->
        <div id="issues-error" class="hidden">
          <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
              <div class="text-red-400">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-red-800" id="error-message">
                  Failed to load sprint issues
                </p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Issues Table -->
        <div id="issues-table-container" class="hidden">
          <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
            <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
              <h4 class="text-lg font-medium text-gray-900">Sprint Issues</h4>
              <p class="text-sm text-gray-600" id="issues-count">Loading...</p>
            </div>
            
            <!-- Desktop Table -->
            <div class="hidden md:block overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Key
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Summary
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Points
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Updated
                    </th>
                  </tr>
                </thead>
                <tbody id="issues-table-body" class="bg-white divide-y divide-gray-200">
                  <!-- Issues will be populated here by JavaScript -->
                </tbody>
              </table>
            </div>
            
            <!-- Mobile Cards -->
            <div class="md:hidden" id="issues-mobile-container">
              <!-- Mobile issue cards will be populated here -->
            </div>
          </div>
        </div>
        
        <!-- Empty State -->
        <div id="issues-empty" class="hidden">
          <div class="text-center py-8">
            <div class="text-gray-400 mb-4">
              <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
            </div>
            <p class="text-gray-500">No issues found in this sprint</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</Modal>

<script>
  // Sprint Issues Modal functionality
  // Following Clean Code: Express intent, single responsibility per function
  
  interface SprintIssue {
    id: string;
    key: string;
    summary: string;
    status: {
      name: string;
      statusCategory: {
        name: string;
      };
    };
    issueType: {
      name: string;
      iconUrl?: string;
      subtask: boolean;
    };
    storyPoints: number | null;
    updated: string;
    jiraUrl: string;
    statusColor: string;
    priorityColor: string;
    shouldShowWarning: boolean;
    warningReason?: string;
    doneTransitionDate?: string;
  }
  
  interface SprintIssuesResponse {
    sprintId: string;
    issues: SprintIssue[];
    totalIssues: number;
    completedIssues: number;
    totalPoints: number;
    completedPoints: number;
    message?: string;
    error?: string;
  }
  
  let currentSprintId: string | null = null;
  
  /**
   * Fetches issues for a specific sprint
   * Following Clean Code: Single responsibility, clear error handling
   */
  async function fetchSprintIssues(sprintId: string): Promise<SprintIssuesResponse | null> {
    try {
      const response = await fetch(`/api/velocity/sprint/${sprintId}/issues`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error fetching sprint issues:', error);
      return null;
    }
  }
  
  /**
   * Shows loading state
   * Following Clean Code: Clear function name, single responsibility
   */
  function showIssuesLoading() {
    document.getElementById('issues-loading')?.classList.remove('hidden');
    document.getElementById('issues-table-container')?.classList.add('hidden');
    document.getElementById('issues-error')?.classList.add('hidden');
    document.getElementById('issues-empty')?.classList.add('hidden');
  }
  
  /**
   * Shows error state with message
   * Following Clean Code: Express intent, parameter validation
   */
  function showIssuesError(message: string) {
    const errorElement = document.getElementById('error-message');
    if (errorElement) {
      errorElement.textContent = message;
    }
    
    document.getElementById('issues-error')?.classList.remove('hidden');
    document.getElementById('issues-loading')?.classList.add('hidden');
    document.getElementById('issues-table-container')?.classList.add('hidden');
    document.getElementById('issues-empty')?.classList.add('hidden');
  }
  
  /**
   * Shows empty state
   * Following Clean Code: Clear intent, consistent state management
   */
  function showIssuesEmpty() {
    document.getElementById('issues-empty')?.classList.remove('hidden');
    document.getElementById('issues-loading')?.classList.add('hidden');
    document.getElementById('issues-table-container')?.classList.add('hidden');
    document.getElementById('issues-error')?.classList.add('hidden');
  }
  
  /**
   * Formats date for display
   * Following Clean Code: Pure function, single responsibility
   */
  function formatDateForDisplay(dateString: string): string {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return 'Invalid date';
    }
  }
  
  /**
   * Gets issue type color based on type name
   * Following Clean Code: Express intent, consistent mapping
   */
  function getIssueTypeColor(issueTypeName: string): string {
    const typeName = issueTypeName.toLowerCase();
    
    if (typeName.includes('story')) return 'bg-green-100 text-green-800';
    if (typeName.includes('bug')) return 'bg-red-100 text-red-800';
    if (typeName.includes('task')) return 'bg-blue-100 text-blue-800';
    if (typeName.includes('epic')) return 'bg-purple-100 text-purple-800';
    if (typeName.includes('improvement')) return 'bg-yellow-100 text-yellow-800';
    if (typeName.includes('sub-task') || typeName.includes('subtask')) return 'bg-gray-100 text-gray-800';
    
    // Default color for unknown types
    return 'bg-gray-100 text-gray-800';
  }
  

  
  /**
   * Renders issues table for desktop
   * Following Clean Code: Template generation, clear structure
   */
  function renderIssuesTable(issues: SprintIssue[]) {
    const tbody = document.getElementById('issues-table-body');
    if (!tbody) return;
    
    tbody.innerHTML = issues.map(issue => `
      <tr class="hover:bg-gray-50 transition-colors duration-150">
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="px-2 py-1 text-xs font-medium rounded-full ${getIssueTypeColor(issue.issueType.name)}">
            ${issue.issueType.name}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center gap-2">
            <a 
              href="${issue.jiraUrl}" 
              target="_blank" 
              rel="noopener noreferrer"
              class="text-blue-600 hover:text-blue-800 font-medium"
            >
              ${issue.key}
            </a>
            ${issue.shouldShowWarning ? `
              <div class="relative inline-block">
                <span 
                  class="completion-warning-icon text-orange-500 cursor-help"
                  title="Issue non inclusa nel calcolo della velocity"
                  data-tooltip="${issue.warningReason || 'Issue non valida per il calcolo della velocity'}"
                >
                  ⚠️
                </span>
              </div>
            ` : ''}
          </div>
        </td>
        <td class="px-6 py-4">
          <div class="text-sm text-gray-900 max-w-xs truncate" title="${issue.summary}">
            ${issue.summary}
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-center">
          <span class="text-sm font-medium text-gray-900">
            ${issue.storyPoints || '-'}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="px-2 py-1 text-xs font-medium rounded-full ${issue.statusColor}">
            ${issue.status.name}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          ${formatDateForDisplay(issue.updated)}
        </td>
      </tr>
    `).join('');
  }
  
  /**
   * Renders issues cards for mobile
   * Following Clean Code: Responsive design, clear structure
   */
  function renderIssuesMobile(issues: SprintIssue[]) {
    const container = document.getElementById('issues-mobile-container');
    if (!container) return;
    
    container.innerHTML = issues.map(issue => `
      <div class="p-4 border-b border-gray-200 last:border-b-0">
        <div class="flex items-start justify-between mb-2">
          <div class="flex items-center gap-2">
            <a 
              href="${issue.jiraUrl}" 
              target="_blank" 
              rel="noopener noreferrer"
              class="text-blue-600 hover:text-blue-800 font-medium"
            >
              ${issue.key}
            </a>
            ${issue.shouldShowWarning ? `
              <div class="relative inline-block">
                <span 
                  class="completion-warning-icon text-orange-500 cursor-help text-sm"
                  title="Issue non inclusa nel calcolo della velocity"
                  data-tooltip="${issue.warningReason || 'Issue non valida per il calcolo della velocity'}"
                >
                  ⚠️
                </span>
              </div>
            ` : ''}
          </div>
          <span class="px-2 py-1 text-xs font-medium rounded-full ${issue.statusColor}">
            ${issue.status.name}
          </span>
        </div>
        <div class="mb-2">
          <span class="px-2 py-1 text-xs font-medium rounded-full ${getIssueTypeColor(issue.issueType.name)}">
            ${issue.issueType.name}
          </span>
        </div>
        <div class="text-sm text-gray-900 mb-2">${issue.summary}</div>
        <div class="flex items-center justify-between text-xs text-gray-500">
          <span>Points: ${issue.storyPoints || '-'}</span>
          <span>Updated: ${formatDateForDisplay(issue.updated)}</span>
        </div>
      </div>
    `).join('');
  }
  
  /**
   * Shows issues table with data
   * Following Clean Code: Compose operations, clear flow
   */
  function showIssuesTable(data: SprintIssuesResponse) {
    const countElement = document.getElementById('issues-count');
    if (countElement) {
      countElement.textContent = `${data.totalIssues} issues (${data.completedIssues} completed)`;
    }
    
    renderIssuesTable(data.issues);
    renderIssuesMobile(data.issues);
    
    // Initialize tooltips after rendering
    setTimeout(() => initializeTooltips(), 0);
    
    document.getElementById('issues-table-container')?.classList.remove('hidden');
    document.getElementById('issues-loading')?.classList.add('hidden');
    document.getElementById('issues-error')?.classList.add('hidden');
    document.getElementById('issues-empty')?.classList.add('hidden');
  }
  
  /**
   * Updates sprint header information
   * Following Clean Code: Single responsibility, clear structure
   */
  function updateSprintHeader(sprintData: any) {
    const headerContainer = document.getElementById('sprint-header-container');
    if (!headerContainer) return;
    
    // Show the header container
    headerContainer.classList.remove('hidden');
    
    // Update points
    const pointsElement = document.getElementById('sprint-points');
    if (pointsElement) {
      pointsElement.textContent = `${sprintData.completedPoints}/${sprintData.committedPoints}`;
    }
    
    // Update completion rate
    const completionElement = document.getElementById('sprint-completion');
    if (completionElement) {
      completionElement.textContent = `${sprintData.completionRate}%`;
    }
    
    // Update status with proper styling
    const statusElement = document.getElementById('sprint-status');
    if (statusElement) {
      const state = sprintData.sprint.state;
      statusElement.textContent = state.charAt(0).toUpperCase() + state.slice(1);
      
      // Remove existing status classes
      statusElement.className = 'text-sm px-3 py-1 rounded-full inline-block';
      
      // Add appropriate status color
      if (state === 'closed') {
        statusElement.classList.add('bg-green-100', 'text-green-800');
      } else if (state === 'active') {
        statusElement.classList.add('bg-blue-100', 'text-blue-800');
      } else {
        statusElement.classList.add('bg-gray-100', 'text-gray-800');
      }
    }
    
    // Update dates
    const datesElement = document.getElementById('sprint-dates');
    if (datesElement) {
      const startDate = formatDateForDisplay(sprintData.sprint.startDate);
      const endDate = formatDateForDisplay(sprintData.sprint.endDate);
      let dateText = `📅 ${startDate} - ${endDate}`;
      
      if (sprintData.sprint.completeDate) {
        const completeDate = formatDateForDisplay(sprintData.sprint.completeDate);
        dateText += ` (Completed: ${completeDate})`;
      }
      
      datesElement.textContent = dateText;
    }
    
    // Update goal if present
    const goalContainer = document.getElementById('sprint-goal-container');
    const goalElement = document.getElementById('sprint-goal');
    if (goalContainer && goalElement) {
      if (sprintData.sprint.goal) {
        goalElement.textContent = sprintData.sprint.goal;
        goalContainer.classList.remove('hidden');
      } else {
        goalContainer.classList.add('hidden');
      }
    }
  }
  
  /**
   * Loads and displays sprint issues
   * Following Clean Code: Orchestrate operations, error handling
   */
  async function loadSprintIssues(sprintId: string) {
    if (currentSprintId === sprintId) return; // Avoid duplicate requests
    
    currentSprintId = sprintId;
    
    // Update sprint header with data from window
    const sprintData = (window as any).currentSprintData;
    if (sprintData) {
      updateSprintHeader(sprintData);
    }
    
    showIssuesLoading();
    
    try {
      const data = await fetchSprintIssues(sprintId);
      
      if (!data) {
        showIssuesError('Failed to load sprint issues');
        return;
      }
      
      if (data.error) {
        showIssuesError(data.error);
        return;
      }
      
      if (data.issues.length === 0) {
        showIssuesEmpty();
        return;
      }
      
      showIssuesTable(data);
      
    } catch (error) {
      console.error('Error loading sprint issues:', error);
      showIssuesError('An unexpected error occurred');
    }
  }
  
  /**
   * Closes the sprint modal
   * Following Clean Code: Clear intent, cleanup
   */
  function closeSprintModal() {
    currentSprintId = null;
    
    // Hide sprint header
    const headerContainer = document.getElementById('sprint-header-container');
    if (headerContainer) {
      headerContainer.classList.add('hidden');
    }
    
    // Hide all content sections
    document.getElementById('issues-loading')?.classList.add('hidden');
    document.getElementById('issues-table-container')?.classList.add('hidden');
    document.getElementById('issues-error')?.classList.add('hidden');
    document.getElementById('issues-empty')?.classList.add('hidden');
    
    (window as any).hideModal();
  }
  
  /**
   * Initializes enhanced tooltips for completion warning icons
   * Following Clean Code: Single responsibility, event handling
   */
  function initializeTooltips() {
    // Remove any existing tooltips
    document.querySelectorAll('.completion-tooltip').forEach(tooltip => tooltip.remove());
    
    const warningIcons = document.querySelectorAll('.completion-warning-icon');
    
    warningIcons.forEach(icon => {
      const tooltipText = icon.getAttribute('data-tooltip');
      if (!tooltipText) return;
      
      let tooltip: HTMLElement | null = null;
      
      icon.addEventListener('mouseenter', () => {
        // Create tooltip
        tooltip = document.createElement('div');
        tooltip.className = 'completion-tooltip fixed z-[9999] px-3 py-2 text-xs text-white bg-gray-800 rounded shadow-lg pointer-events-none max-w-xs';
        tooltip.textContent = tooltipText;
        
        // Append to body to avoid clipping
        document.body.appendChild(tooltip);
        
        // Position tooltip dynamically based on available space
        const iconRect = icon.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();

        const viewportWidth = window.innerWidth;
        
        // Calculate preferred position (above the icon)
        let top = iconRect.top - tooltipRect.height - 8;
        let left = iconRect.left + (iconRect.width / 2) - (tooltipRect.width / 2);
        
        // Check if tooltip would go above viewport
        if (top < 10) {
          // Position below the icon instead
          top = iconRect.bottom + 8;
          tooltip.classList.add('tooltip-below');
        } else {
          tooltip.classList.add('tooltip-above');
        }
        
        // Check if tooltip would go outside left/right viewport
        if (left < 10) {
          left = 10;
        } else if (left + tooltipRect.width > viewportWidth - 10) {
          left = viewportWidth - tooltipRect.width - 10;
        }
        
        tooltip.style.top = `${top}px`;
        tooltip.style.left = `${left}px`;
      });
      
      // Close tooltip function
      const closeTooltip = () => {
        if (tooltip) {
          tooltip.remove();
          tooltip = null;
        }
      };
      
      icon.addEventListener('mouseleave', closeTooltip);
      
      // Also close tooltip on scroll or resize to prevent misalignment
      icon.addEventListener('mouseenter', () => {
        window.addEventListener('scroll', closeTooltip, { passive: true });
        window.addEventListener('resize', closeTooltip, { passive: true });
      });
      
      icon.addEventListener('mouseleave', () => {
        window.removeEventListener('scroll', closeTooltip);
        window.removeEventListener('resize', closeTooltip);
      });
    });
  }
  
  // Make functions available globally
  (window as any).loadSprintIssues = loadSprintIssues;
  (window as any).closeSprintModal = closeSprintModal;
  (window as any).initializeTooltips = initializeTooltips;
</script>

<style>
  /* Sprint modal specific styles */
  .sprint-info {
    @apply max-w-none;
  }
  
  /* Table hover effects */
  tbody tr:hover {
    @apply bg-gray-50;
  }
  
  /* Mobile responsive adjustments */
  @media (max-width: 768px) {
    .sprint-info .grid {
      @apply grid-cols-1 gap-2;
    }
  }
  
  /* Completion warning icon styles */
  .completion-warning-icon {
    @apply transition-all duration-200;
  }
  
  .completion-warning-icon:hover {
    @apply transform scale-110;
  }
  
  /* Enhanced tooltip styles */
  .completion-tooltip {
    white-space: normal;
    word-wrap: break-word;
    line-height: 1.4;
    backdrop-filter: blur(4px);
  }
  
  /* Arrow for tooltip positioned above icon */
  .completion-tooltip.tooltip-above::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -4px;
    border-width: 4px;
    border-style: solid;
    border-color: #374151 transparent transparent transparent;
  }
  
  /* Arrow for tooltip positioned below icon */
  .completion-tooltip.tooltip-below::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    margin-left: -4px;
    border-width: 4px;
    border-style: solid;
    border-color: transparent transparent #374151 transparent;
  }
  
  /* Smooth animation for tooltip appearance */
  .completion-tooltip {
    animation: tooltipFadeIn 0.2s ease-out;
  }
  
  @keyframes tooltipFadeIn {
    from {
      opacity: 0;
      transform: translateY(-4px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style> 