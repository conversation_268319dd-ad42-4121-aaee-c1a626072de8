# Jira Configuration
JIRA_BASE_URL=https://yourcompany.atlassian.net/
JIRA_EMAIL=<EMAIL>
JIRA_API_TOKEN=your_api_token_here
JIRA_PROJECT_KEY=PROJ

# Application Configuration
NODE_ENV=development
CACHE_TTL=300000

# Database Configuration
# Choose one: turso | postgres | mysql | local-sqlite | mock
DATABASE_PROVIDER=mock

# Turso Configuration (if using Turso)
TURSO_DATABASE_URL=libsql://your-db.turso.io
TURSO_AUTH_TOKEN=your-turso-auth-token
# Optional Turso settings
TURSO_SYNC_URL=
TURSO_SYNC_INTERVAL=5000
TURSO_ENCRYPTION_KEY=

# Cache Configuration
VELOCITY_CACHE_ENABLED=true
VELOCITY_CACHE_MAX_AGE_HOURS=24
DATABASE_RETENTION_DAYS=365

# Performance Configuration
VELOCITY_BATCH_SIZE=10
ENABLE_ISSUES_STORAGE=true
ENABLE_METRICS_CALCULATION=true
